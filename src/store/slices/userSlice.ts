import {
  createUser,
  getPreSignedUrl,
  getUserById,
  getUserProfile,
  updateSimplifiedDiet,
  updateUserProfile,
  updateUserProfileByPatch,
  userOnboarding,
} from "@/services/api/userAPI"; // Adjust the path based on your project
import { User, UserOnboarding } from "@/types/schemas/user";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index"; // Adjust the import based on your project structure

// Define the state interface
interface UserState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: UserState = {
  user: null,
  loading: false,
  error: null,
};

// Async thunk for fetching user profile
export const fetchUserProfile = createAsyncThunk<User>(
  "user/fetchProfile",
  async () => {
    try {
      const profile = await getUserProfile();
      return profile;
    } catch (error) {
      console.error("Error fetching profile:", error);
      throw error; // Ensures the thunk gets rejected if there's an error
    }
  }
);

// Async thunk for fetching user by ID
export const fetchUserById = createAsyncThunk<User>(
  "user/fetchUserById",
  async (_, { rejectWithValue }) => {
    try {
      const profile = await getUserById(); // Call getUserById without passing a userId
      return profile;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Async thunk for updating user profile with patch
export const updateUserProfileByPatchThunk = createAsyncThunk<
  User,
  { url: string }
>("user/updateUserProfileByPatch", async (profileData) => {
  try {
    const updatedProfile = await updateUserProfileByPatch(profileData);
    return updatedProfile;
  } catch (error) {
    throw error;
  }
});

export const userOnboardingThunk = createAsyncThunk(
  "user/userOnboarding",
  async (userData: UserOnboarding, { rejectWithValue }) => {
    try {
      const userOnboarded = await userOnboarding(userData);
      return userOnboarded === 1;
    } catch (error) {
      console.log("error", error);
      return rejectWithValue(error);
    }
  }
);

// Async thunk for getting pre-signed URL
export const getPreSignedUrlThunk = createAsyncThunk<
  { id: number; url: string },
  { id: number; fileName: string }
>("user/getPreSignedUrl", async ({ id, fileName }) => {
  try {
    const preSignedUrl = await getPreSignedUrl(id, fileName);
    return { id, url: preSignedUrl };
  } catch (error) {
    throw error;
  }
});

// Async thunk for creating a new user
export const createUserThunk = createAsyncThunk<
  User,
  {
    email: string;
    name: string;
    nickname: string;
    userSubId: string;
  }
>("user/createUser", async (userData) => {
  try {
    const newUser = await createUser(userData);
    return newUser;
  } catch (error) {
    throw error;
  }
});

// Async thunk for updating user profile
export const updateUserProfileThunk = createAsyncThunk<User, Partial<User>>(
  "user/updateProfile",
  async (profileData) => {
    try {
      const updatedProfile = await updateUserProfile(profileData);
      return updatedProfile;
    } catch (error) {
      throw error;
    }
  }
);

export const updateSimplifiedDietThunk = createAsyncThunk<
  User,
  { isSimplifiedDiet: boolean }
>(
  "user/updateSimplifiedDiet",
  async ({ isSimplifiedDiet }, { rejectWithValue }) => {
    try {
      const updatedUser = await updateSimplifiedDiet(isSimplifiedDiet);
      if(updatedUser.statusCode === 200){
        return { ...updatedUser, isSimplifiedDiet } as User;
      }
      return { ...updatedUser, isSimplifiedDiet: !isSimplifiedDiet } as User;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Slice creation
const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    updateImage: (state, action) => {
      state.user = action.payload;
    },
    updateUser: (state, action) => {
      state.user = action.payload;
    },
    // Action to clear/reset the profile data if needed
    resetProfile: (state) => {
      state.user = null;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchUserProfile.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
          state.user = action.payload;
        }
      )
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to fetch user profile";
      })
      .addCase(fetchUserById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchUserById.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
          state.user = action.payload;
        }
      )
      .addCase(fetchUserById.rejected, (state, action) => {
        state.loading = false;
        state.error = action?.error?.message ?? "Failed to fetch user by ID";
      })
      .addCase(updateUserProfileByPatchThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(userOnboardingThunk.fulfilled, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        updateUserProfileByPatchThunk.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
        }
      )
      .addCase(updateUserProfileByPatchThunk.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message ?? "Failed to update user profile with patch";
      })
      .addCase(getPreSignedUrlThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        getPreSignedUrlThunk.fulfilled,
        (state, action: PayloadAction<{ id: number; url: string }>) => {
          state.loading = false;
        }
      )
      .addCase(getPreSignedUrlThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to fetch pre-signed URL";
      })
      .addCase(createUserThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        createUserThunk.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
          state.user = action.payload;
        }
      )
      .addCase(createUserThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to create user";
      })
      .addCase(updateUserProfileThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        updateUserProfileThunk.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
        }
      )
      .addCase(updateUserProfileThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? "Failed to update user profile";
      })
      .addCase(updateSimplifiedDietThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        updateSimplifiedDietThunk.fulfilled,
        (state, action: PayloadAction<User>) => {
          state.loading = false;
          if (state.user) {
            state.user.isSimplifiedDiet = action.payload.isSimplifiedDiet;
          }
        }
      )
      .addCase(updateSimplifiedDietThunk.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message ?? "Failed to update simplified diet setting";
      });
  },
});

// Export actions from reducers
export const { resetProfile, updateImage, updateUser, setLoading } =
  userSlice.actions;

// Selector to access user profile from the state
export const selectUserProfile = (state: RootState) => state.user.user;
export const selectUserLoading = (state: RootState) => state.user.loading;
export const selectUserError = (state: RootState) => state.user.error;

// Export the reducer to be added to the store
export default userSlice.reducer;
