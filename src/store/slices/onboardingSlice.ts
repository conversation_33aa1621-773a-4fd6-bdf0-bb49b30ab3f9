import { createSlice } from '@reduxjs/toolkit';
import { RootState } from '../index';

interface OnboardingState {
  completed: boolean;
  isPreferenceSelected: boolean;
  isTutorial: boolean;
}

const initialState: OnboardingState = {
  completed: false,
  isPreferenceSelected: false,
  isTutorial: false
};

const onboardingSlice = createSlice({
  name: 'onboarding',
  initialState,
  reducers: {
    completeOnboarding: (state) => {
      state.completed = true;
    },
    resetOnboarding: (state) => {
      state.completed = false;
      state.isPreferenceSelected = false;
    },
    setPrefrenceSelected: (state, action) => {
      state.isPreferenceSelected = action.payload;
    },
    setTutorial: (state, action) => {
      state.isTutorial = action.payload;
    }
  },
});

export const { completeOnboarding, resetOnboarding, setPrefrenceSelected, setTutorial } = onboardingSlice.actions;

export const selectOnboardingCompleted = (state: RootState) => state.onboarding.completed;
export const selectIsPreferenceSelected = (state: RootState) => state.onboarding.isPreferenceSelected;
export const selectOnboarding = (state: RootState) => state.onboarding;

export default onboardingSlice.reducer;
