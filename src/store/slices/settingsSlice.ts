import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { getFeatureUpdate } from "@/services/api/userAPI";
import DeviceInfo from "react-native-device-info";
import { checkAndStoreCurrentVersion } from "@/utils/versionStorage";

// Define state
interface SettingsState {
  showWhatsNewModal: boolean;
  consumptionUnit: "Phe" | "Protein";
  isSimplifiedDiet: boolean;
  featureUpdate?: {
    version: string;
    description: string;
    pictureUrl: string;
    title: string;
  } | null;
  currentAppVersion: string; // Current installed app version (e.g., 1.2.3)
  lastAcknowledgedVersion: string; // Last version user pressed "Got it"
}

const initialState: SettingsState = {
  showWhatsNewModal: false,
  consumptionUnit: "Phe",
  isSimplifiedDiet: false,
  featureUpdate: null,
  currentAppVersion: "0",
  lastAcknowledgedVersion: "0",
};

export const fetchFeatureUpdate = createAsyncThunk(
  "settings/fetchFeatureUpdate",
  async (_, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setShowWhatsNewModal(false));
      const isNew = checkAndStoreCurrentVersion();
      if (isNew) {
        const appVersionCode = DeviceInfo.getVersion();
        const response = await getFeatureUpdate(appVersionCode);
        if (response && response.version) {
          dispatch(setFeatureUpdate(response));
          dispatch(setShowWhatsNewModal(true));
        } else {
          dispatch(setFeatureUpdate(null));
          dispatch(setShowWhatsNewModal(false));
        }
        return response;
      }
      else dispatch(setFeatureUpdate(null));
    } catch (error) {
      dispatch(setFeatureUpdate(null));
      dispatch(setShowWhatsNewModal(false));
      return rejectWithValue(error);
    }
  }
);

const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    setShowWhatsNewModal(state, action: PayloadAction<boolean>) {
      state.showWhatsNewModal = action.payload;
    },
    setConsumptionUnit(state, action: PayloadAction<"Phe" | "Protein">) {
      state.consumptionUnit = action.payload;
    },
    setIsSimplifiedDiet(state, action: PayloadAction<boolean>) {
      state.isSimplifiedDiet = action.payload;
    },
    resetSettingsState: () => initialState,
    setCurrentAppVersion(state, action: PayloadAction<string>) {
      state.currentAppVersion = action.payload;
    },
    setLastAcknowledgedVersion(state, action: PayloadAction<string>) {
      state.lastAcknowledgedVersion = action.payload;
    },

    setFeatureUpdate(
      state,
      action: PayloadAction<{
        version: string;
        description: string;
        pictureUrl: string;
        title: string;
      } | null>
    ) {
      state.featureUpdate = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchFeatureUpdate.fulfilled, (state, action) => {
      if (action.payload) {
        state.featureUpdate = action.payload;
      }
    });
  },
});

// Export actions
export const {
  setShowWhatsNewModal,
  resetSettingsState,
  setConsumptionUnit,
  setIsSimplifiedDiet,
  setCurrentAppVersion,
  setLastAcknowledgedVersion,
  setFeatureUpdate,
} = settingsSlice.actions;

// Selectors
export const selectShowWhatsNewModal = (state: { settings: SettingsState }) =>
  state.settings.showWhatsNewModal;
export const selectFeatureUpdate = (state: { settings: SettingsState }) =>
  state.settings.featureUpdate;
export const selectConsumptionUnit = (state: { settings: SettingsState }) =>
  state.settings.consumptionUnit;
export const selectIsSimplifiedDiet = (state: { settings: SettingsState }) =>
  state.settings.isSimplifiedDiet;

export default settingsSlice.reducer;
