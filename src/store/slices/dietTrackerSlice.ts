import { SearchBy } from "@/constants/searchFood";
import {
  analyseFood as analyseFoodAPI,
  createFood as createFoodAP<PERSON>,
  createFoodEntry as createFoodEntryAPI,
  deleteFoodEntry as deleteFoodEntryAPI,
  getPortions as getPortionsAPI,
  listFoodEntries as listFoodEntriesAPI,
  searchFood as searchFoodAPI,
  updateFoodEntry as updateFoodEntryAPI,
} from "@/services/api/dietTrackerAPI";
import {
  CreateFoodEntryPayload,
  CreateFoodPayload,
  DietStatus,
  Food,
  FoodEntryItem,
  Portions,
} from "@/types/schemas/dietTracker";
import { PortionConverter } from "@/utils/portions";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { selectConsumptionUnit } from "./settingsSlice";

export interface ScanItem {
  description: string;
  quantity: number;
  unit: string;
  gram_weight: number;
  phe: number;
  protein: number;
  food_id?: string;
}

interface IScanMeal {
  analysis_id: string;
  items?: ScanItem[];
  result?: ScanItem[];
}

interface DietTrackerState {
  foodItemsByFrequency: Food[];
  foodItemsByUser: Food[];
  portions: Portions[];
  foodEntries: any[];
  scanResult: IScanMeal | undefined;
  matchFoods?: any;
  selectedDietDate: Date;
  selectedTimeSlot: "morning" | "afternoon" | "evening";
  scanStatus: "idle" | "loading" | "success" | "error";
  status: { manualEntry?: DietStatus };
  loading: boolean;
  error: string | null;
  success: boolean;
}

const initialState: DietTrackerState = {
  foodItemsByFrequency: [],
  foodItemsByUser: [],
  portions: [],
  foodEntries: [],
  matchFoods: [],
  status: { manualEntry: DietStatus.IDLE },
  scanResult: undefined,
  scanStatus: "idle",
  selectedDietDate: new Date(),
  selectedTimeSlot: "morning",
  loading: false,
  error: null,
  success: false,
};

export const searchFoodByFrequency = createAsyncThunk<
  Food[],
  string,
  { state: RootState; rejectValue: string }
>(
  "dietTracker/searchFoodByFrequency",
  async (searchQuery, { rejectWithValue, getState }) => {
    try {
      const state = getState();
      const consumptionType = selectConsumptionUnit(state);

      const response = await searchFoodAPI(
        searchQuery,
        SearchBy.frequency,
        consumptionType
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to search food by frequency."
      );
    }
  }
);

export const searchFoodByUser = createAsyncThunk<
  Food[],
  string,
  { state: RootState; rejectValue: string }
>(
  "dietTracker/searchFoodByUser",
  async (searchQuery, { rejectWithValue, getState }) => {
    try {
      const state = getState();
      const consumptionType = selectConsumptionUnit(state);

      const response = await searchFoodAPI(
        searchQuery,
        SearchBy.recency,
        consumptionType
      );

      return response;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to search food by user."
      );
    }
  }
);
export const createFood = createAsyncThunk<
  Food,
  CreateFoodPayload // Use the new interface
>("dietTracker/createFood", async (payload, { rejectWithValue }) => {
  try {
    const response = await createFoodAPI(payload);

    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to create food entry"
    );
  }
});

// Async thunk for fetching portions
export const fetchPortions = createAsyncThunk<
  Portions[],
  void, // No arguments needed
  { rejectValue: string }
>("dietTracker/fetchPortions", async (_, { rejectWithValue }) => {
  try {
    const response = await getPortionsAPI();
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch portions"
    );
  }
});

// Async thunk for creating a food entry
export const createFoodEntry = createAsyncThunk<
  FoodEntryItem, // Adjust the type based on the response structure
  { description: string; time: Date; items: FoodEntryItem[] }, // Payload structure
  { rejectValue: string }
>("dietTracker/createFoodEntry", async (entryData, { rejectWithValue }) => {
  try {
    const response = await createFoodEntryAPI(entryData);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to create food entry"
    );
  }
});

// Async thunk for fetching food entries
export const fetchFoodEntries = createAsyncThunk<
  any[], // Adjust the type based on the API response structure
  { date: string }, // Payload structure
  { rejectValue: string }
>("dietTracker/fetchFoodEntries", async ({ date }, { rejectWithValue }) => {
  try {
    return await listFoodEntriesAPI(date);
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to fetch food entries"
    );
  }
});

export const deleteFoodEntry = createAsyncThunk<
  void,
  string, // Accept only entryId as payload
  { rejectValue: string }
>(
  "dietTracker/deleteFoodEntry",
  async (entryId, { rejectWithValue, dispatch }) => {
    try {
      await deleteFoodEntryAPI(entryId); // Call the DELETE API

      // Remove the entry from the Redux store
      dispatch(removeFoodEntry(entryId));
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete food entry"
      );
    }
  }
);

export const updateFoodEntry = createAsyncThunk<
  any, // Adjust the response type as necessary
  {
    entryId: string;
    time: string;
    description: string;
    items: {
      description: string;
      quantity: number;
      unit: string;
      gram_weight: number;
      phe: number;
      protein: number;
      food_id: string;
    }[];
  }, // Payload structure
  { rejectValue: string }
>("dietTracker/updateFoodEntry", async (entryData, { rejectWithValue }) => {
  try {
    const response = await updateFoodEntryAPI(entryData);
    return response; // Optionally return the response
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to update food entry"
    );
  }
});

// Async thunk for analyzing food
export const analyseFood = createAsyncThunk<
  any,
  { formData: FormData }, // Update the type to include `uri`
  { rejectValue: string }
>("dietTracker/analyseFood", async ({ formData }, { rejectWithValue }) => {
  try {
    const response = await analyseFoodAPI(formData);
    return response;
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to analyze food."
    );
  }
});

// AsyncThunk: Save Meal
export const saveMeal = createAsyncThunk<
  void,
  { createFoodPayload: CreateFoodPayload; foodEntriesToDelete: string[] }
>("dietTracker/saveMeal", async (payload, { rejectWithValue }) => {
  const { createFoodPayload, foodEntriesToDelete } = payload;

  try {
    // Step 1: Create the food using createFoodAPI
    const foodResponse = await createFoodAPI(createFoodPayload);

    const mealItem = {
      description: foodResponse.description,
      time: createFoodPayload.time,
      quantity: foodResponse.quantity || 0,
      unit: foodResponse.unit || "g",
      gram_weight: PortionConverter.toGrams(
        foodResponse.unit,
        foodResponse.quantity
      ),
      phe:
        foodResponse.nutrients?.find(
          (n: { name: string }) => n.name?.toLowerCase() === "phe"
        )?.amount || 0,
      protein:
        foodResponse.nutrients?.find(
          (n: { name: string }) => n.name?.toLowerCase() === "protein"
        )?.amount || 0,
      food_id: foodResponse._id,
      user_flags: { is_free: false },
    };

    const entryItems = [mealItem];

    // Step 2: Delete each food entry
    for (const entryId of foodEntriesToDelete) {
      await deleteFoodEntryAPI(entryId);
    }

    // Step 3: Create a new food entry with the created food's ingredients
    const createFoodEntryPayload: CreateFoodEntryPayload = {
      description: createFoodPayload.description,
      time: createFoodPayload.time,
      items: entryItems,
    };

    await createFoodEntryAPI(createFoodEntryPayload);
  } catch (error: any) {
    console.error(error);
    return rejectWithValue(
      error.response?.data?.message || "Failed to save meal"
    );
  }
});

const dietTrackerSlice = createSlice({
  name: "dietTracker",
  initialState,
  reducers: {
    cleanApiState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    resetDietState: () => {
      return initialState;
    },
    selectDietDate: (state, action) => {
      state.selectedDietDate = action.payload;
    },
    selectTimeSlot: (state, action) => {
      state.selectedTimeSlot = action.payload;
    },
    removeFoodEntry: (state, action) => {
      const entryId = action.payload;
      state.foodEntries = state.foodEntries.filter(
        (entry) => entry._id !== entryId
      );
    },
    clearScanData: (state) => {
      state.matchFoods = [];
      state.scanResult = undefined;
      state.scanStatus = "idle";
    },
    clearSearch: (state) => {
      state.foodItemsByFrequency = [];
      state.error = null;
      state.loading = false;
    },
    editingMatchFood: (state, action: PayloadAction<FoodEntryItem[]>) => {
      const normalizedFoods = action.payload.map((food) => ({
        ...food,
        user_flags: {
          ...food.user_flags,
          is_free: food?.user_flags?.is_free ?? false,
        },
      }));

      state.matchFoods = normalizedFoods;
    },

    updateScanStatus: (state, action) => {
      state.scanStatus = action.payload;
    },
    setManualEntryStatus: (state, action) => {
      state.status = { manualEntry: action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Search food by frequency
      .addCase(searchFoodByFrequency.pending, (state) => {
        // state.foodItemsByFrequency = [];
        state.loading = true;
        state.error = null;
      })
      .addCase(searchFoodByFrequency.fulfilled, (state, action) => {
        state.loading = false;
        state.foodItemsByFrequency = action.payload; // Save frequency-based results
      })
      .addCase(searchFoodByFrequency.rejected, (state, action) => {
        state.loading = false;
        state.foodItemsByFrequency = [];
        state.error =
          action.payload || "An error occurred while searching by frequency.";
      })
      // Search food by user
      .addCase(searchFoodByUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchFoodByUser.fulfilled, (state, action) => {
        state.loading = false;
        state.foodItemsByUser = action.payload; // Save user-based results
      })
      .addCase(searchFoodByUser.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.payload || "An error occurred while searching by user.";
      })
      // Get portions
      .addCase(fetchPortions.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchPortions.fulfilled, (state, action) => {
        state.portions = action.payload;
      })
      .addCase(fetchPortions.rejected, (state, action) => {
        state.error =
          action.payload || "An error occurred while fetching portions.";
      })
      // Create Food Entry
      .addCase(createFoodEntry.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createFoodEntry.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(createFoodEntry.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.payload || "An error occurred while creating the food entry.";
        state.success = false;
      })
      // Create Food
      .addCase(createFood.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createFood.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(createFood.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action?.payload || "An error occurred while creating the food entry.";
        state.success = false;
      })
      // Fetch food entries reducers
      .addCase(fetchFoodEntries.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFoodEntries.fulfilled, (state, action) => {
        state.loading = false;
        state.foodEntries = action.payload; // Store food entries
      })
      .addCase(fetchFoodEntries.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.payload || "An error occurred while fetching food entries.";
      }) // Handle delete food entry
      .addCase(deleteFoodEntry.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteFoodEntry.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(deleteFoodEntry.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete food entry from API.";
      })
      // Handle update food enrtry
      .addCase(updateFoodEntry.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateFoodEntry.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(updateFoodEntry.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.payload || "An error occurred while updating the food entry.";
        state.success = false;
      })
      // Analyze Food
      .addCase(analyseFood.pending, (state) => {
        state.scanStatus = "loading";
        state.error = null;
        state.scanResult = undefined;
      })
      .addCase(analyseFood.fulfilled, (state, action) => {
        state.scanStatus = "success";
        state.scanResult = undefined; //action.payload; // Save analysis result
        state.matchFoods = action.payload;
      })
      .addCase(analyseFood.rejected, (state, action) => {
        state.scanStatus = "error";
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : JSON.stringify(action.payload) || "Failed to analyze food.";
      })
      // Save meal
      .addCase(saveMeal.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(saveMeal.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(saveMeal.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.success = false;
      });
  },
});

// Actions
export const {
  resetDietState,
  removeFoodEntry,
  selectDietDate,
  cleanApiState,
  selectTimeSlot,
  clearScanData,
  editingMatchFood,
  updateScanStatus,
  clearSearch,
  setManualEntryStatus,
} = dietTrackerSlice.actions;

// Selectors
export const selectFoodItemsByFrequency = (state: RootState) =>
  state.dietTracker.foodItemsByFrequency;

export const selectFoodItemsByUser = (state: RootState) =>
  state.dietTracker.foodItemsByUser;

export const selectPortions = (state: RootState) => state.dietTracker.portions;
export const selectDietTrackerLoading = (state: RootState) =>
  state.dietTracker.loading;
export const selectDietTrackerError = (state: RootState) =>
  state.dietTracker.error;
export const selectFoodEntrySuccess = (state: RootState) =>
  state.dietTracker.success;
export const selectFoodEntries = (state: RootState) =>
  state.dietTracker.foodEntries;

export const selectDietTracker = (state: RootState) => state.dietTracker;

export default dietTrackerSlice.reducer;
