import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { handleTimestamp } from "@/utils/taskManager";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  CreateMedicationDetailsPayload,
  Sections,
  TaskStatus,
} from "@/types/schemas/task";
import {
  clearFromulaDetails,
  clearTaskDetails,
  selectTask,
  setIsFromUpdate,
} from "@/store/slices/taskManager/taskManager.slice";
import {
  createMedicationDetails,
  updateMedicationDetails,
  fetchMedicationTasks,
  fetchTaskById,
  createFormulaDetails,
  updateFormulaDetails,
  fetchFormulaTasks,
  fetchFormulaById,
  fetchFormulaUnitList,
} from "@/store/slices/taskManager/taskManager.middleware";
import { MOCK_DATA } from "@/mock-data/mockData";
import { MedicationFormPayload } from "@/shared/interfaces/task-manager.interface";
import {
  FORMULA_FORM_FIRST_STEP_VALIDATION_SCHEMA,
  FORMULA_FORM_SECOND_STEP_VALIDATION_SCHEMA,
  MEDICATION_FORM_FIRST_STEP_VALIDATION_SCHEMA,
  MEDICATION_FORM_SECOND_STEP_VALIDATION_SCHEMA,
  MEDICATION_FORM_THIRD_STEP_VALIDATION_SCHEMA,
} from "@/shared/input-validations/task-manager.validation";
import { useAnalytics, analyticsEventType } from "@/hooks/useAnalytics";
import { Alert } from "react-native";

interface DoseDetails {
  patientRoutineDetailId: number | null;
  categoryTypeId?: {
    id: number;
    label: string;
    value: number;
  };
  quantity: string | number;
  strength: string | number;
}

const MEDICATION_FORM_VALIDATION_SCHEMA = [
  {
    schema: MEDICATION_FORM_FIRST_STEP_VALIDATION_SCHEMA,
    fields: ["medicineName", "activeIngredient", "categoryTypeId"],
  },
  {
    schema: MEDICATION_FORM_SECOND_STEP_VALIDATION_SCHEMA,
    fields: ["medicineName", "doses"],
  },
  {
    schema: MEDICATION_FORM_THIRD_STEP_VALIDATION_SCHEMA,
    fields: ["intakeTime", "fromDate", "toDate"],
  },
];

const FORMULA_FORM_VALIDATION_SCHEMA = [
  {
    schema: FORMULA_FORM_FIRST_STEP_VALIDATION_SCHEMA,
    fields: ["formulaName", "quantity"],
  },
  {
    schema: FORMULA_FORM_SECOND_STEP_VALIDATION_SCHEMA,
    fields: ["frequencyTypeId", "reminder", "fromDate", "toDate"],
  },
];

const useTaskManagerContainer = () => {
  const [medFormStep, setMedFormStep] = useState<number>(2);
  const [formulaFormStep, setFormulaFormStep] = useState<number>(1);
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
  const [isCustomFilled, setIsCustomFilled] = useState<boolean>(false);
  const { combineDateAndTimeToUTC } = handleTimestamp;

  const { status, formulaUnitList } = useAppSelector(selectTask);
  const [selectedFormulaUnit, setFormulaUnit] = useState(
    formulaUnitList?.length > 0 ? formulaUnitList?.[0] : ""
  );

  const medModalRef = useRef(null);
  const formulaModalRef = useRef(null);
  const { setAnalyticsEvent } = useAnalytics();

  // medication details form
  const {
    control: medicationFormControl,
    handleSubmit: handleMedicationSubmit,
    reset: resetMedicationForm,
    setValue: setMedicationValue,
    trigger: triggerMedFormFields,
    getValues: getMedicationFormValues,
    unregister,
  } = useForm({
    mode: "all",
    resolver: yupResolver<MedicationFormPayload>(
      MEDICATION_FORM_VALIDATION_SCHEMA[medFormStep - 1]?.schema
    ),
    defaultValues: {
      id: null,
      fromUpdate: false,
      medicineName: "",
      activeIngredient: "",
      doses: [
        {
          patientRoutineDetailId: null,
          categoryTypeId: undefined,
          quantity: "",
          strength: "",
        },
      ],
      reminder: undefined,
      intakeTime: new Date().getTime(),
      fromDate: new Date().toISOString(),
      toDate: "",
      alertBefore: null,
      customSchedule: {
        frequency: {
          id: 2,
          value: "Day",
          label: "Daily",
        },
        alertBefore: 0,
        interval: {
          id: 1,
          value: "1st",
          label: "1",
        },
        isEachSelected: true,
        monthDates: [],
        months: [],
        weekDays: [],
        weekIndex: null,
      },
    },
  });

  // formula details form
  const {
    control: formulaFormControl,
    handleSubmit: handleFormulaSubmit,
    reset: resetFormulaForm,
    trigger: triggerFormulaFormFields,
    setValue: setFormulaValue,
  } = useForm({
    mode: "all",
    resolver: yupResolver(
      FORMULA_FORM_VALIDATION_SCHEMA[formulaFormStep - 1]?.schema
    ),
    defaultValues: {
      id: null,
      fromUpdate: false,
      intakeTime: new Date().getTime(),
      fromDate: new Date().toISOString(),
      toDate: "",
      formulaName: "",
      quantity: undefined,
      frequencyTypeId: undefined,
      formulaUnit: selectedFormulaUnit,
      reminder: undefined,
      customSchedule: {
        frequency: {
          id: 2,
          value: "Day",
          label: "Daily",
        },
        interval: {
          id: 1,
          value: "1st",
          label: "1",
        },
        isEachSelected: false,
        monthDates: [],
        months: [],
        weekDays: [],
        weekIndex: null,
      },
    },
  });

  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.user);
  const firstName = user?.name?.split(" ")?.[0] ?? "";

  // fetching formula unit list
  useEffect(() => {
    dispatch(fetchFormulaUnitList());
  }, [dispatch]);

  function handleAddFormula() {
    setIsCustomFilled(false);
    setSelectedTaskId(null);
    resetFormulaForm();
    setFormulaFormStep(1);
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "tasks_formula_task_started",
      item_id: "tasks_formula_task_started",
      action: "User started formula task",
    });
    formulaModalRef.current?.open();
  }

  function handleAddMedication() {
    setIsCustomFilled(false);
    setSelectedTaskId(null);
    resetMedicationForm();
    setMedFormStep(2);
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "tasks_medication_task_started",
      item_id: "tasks_medication_task_started",
      action: "User started adding medication task",
    });
    medModalRef.current?.open();
  }

  async function handleMedicationSummary(id: number, summary: boolean | false) {
    setSelectedTaskId(id);

    dispatch(setIsFromUpdate(Sections.MEDICATION));
    await dispatch(fetchTaskById(id));
    if (summary) {
      setMedFormStep(5);
      medModalRef.current?.open();
    } else {
      medModalRef.current?.close();
    }
  }

  async function handleFormulaSummary(id: number, summary?: boolean | false) {
    if (summary) {
      setFormulaFormStep(3);
      formulaModalRef.current?.open();
    } else {
      formulaModalRef.current?.close();
    }

    setSelectedTaskId(id);
    dispatch(setIsFromUpdate(Sections.FORMULA));
    await dispatch(fetchFormulaById(id));
  }

  function handleMedModalBackPress() {
    if (typeof selectedTaskId === "number" && medFormStep === 2) {
      setMedFormStep(5);
      return;
    }
    setMedFormStep((prev) => prev - 1);
  }

  function resetSheet() {
    setIsCustomFilled(false);
    setSelectedTaskId(null);
    dispatch(setIsFromUpdate(null));
    dispatch(clearTaskDetails());
    dispatch(clearFromulaDetails());
  }

  function handleMedModalClosePress() {
    setMedFormStep(2);
    resetMedicationForm();
    resetSheet();
  }

  function handleFormulaModalClosePress() {
    setFormulaFormStep(1);
    resetFormulaForm();
    resetSheet();
  }

  async function handleMedModalNextPress() {
    const isStepValid = await triggerMedFormFields(
      MEDICATION_FORM_VALIDATION_SCHEMA[medFormStep - 1].fields
    );
    if (isStepValid) {
      setMedFormStep((prev) => prev + 1);
    }
  }

  function getFrequencyName(label: string) {
    switch (label) {
      case "Daily":
        return "Day";
      case "Weekly":
        return "Week";
      case "Monthly":
        return "Month";
      case "Yearly":
        return "Year";
      default:
        return "Day";
    }
  }

  async function handleMedSubmitForm(data: CreateMedicationDetailsPayload) {
    const disableOnMedicationLoading =
      status.updateMedicationDetails === TaskStatus.LOADING ||
      status.createMedicationStatus === TaskStatus.LOADING;
    const disableOnFormulaLoading =
      status.updateMedicationDetails === TaskStatus.LOADING ||
      status.createFormulaTaskStatus === TaskStatus.LOADING;
    if (disableOnMedicationLoading || disableOnFormulaLoading) {
      return;
    }

    const isCustom = Number(data?.frequencyTypeId?.id) === 11;
    if (isCustom && !isCustomFilled && medFormStep > 3) {
      setIsCustomFilled(true);
      handleMedModalBackPress();
      return;
    }

    const {
      id,
      medicineName,
      activeIngredient,
      quantity,
      doses,
      strength,
      intakeTime,
      toDate,
      fromDate,
      reminder,
      frequencyTypeId,
      customSchedule,
    } = data || {};

    const utcDateTime = combineDateAndTimeToUTC(fromDate, intakeTime);

    const customSchedulePayload = isCustom
      ? {
          interval: customSchedule?.interval?.id || 1,
          frequencyTypeId: customSchedule?.frequency?.id,
          monthDates: customSchedule?.monthDates?.length
            ? customSchedule?.monthDates
            : null,
          months: customSchedule?.months?.length
            ? customSchedule?.months
            : null,
          weekDays: customSchedule?.weekDays?.length
            ? customSchedule?.weekDays
            : null,
          weekIndex: customSchedule?.weekIndex ?? null,
        }
      : null;

    const medicationDetails = doses.map((dose: DoseDetails) => ({
      categoryTypeId: dose.categoryTypeId?.id || dose.categoryTypeId,
      quantity: Number(dose?.quantity),
      strength: Number(dose?.strength),
      patientRoutineDetailId: dose?.patientRoutineDetailId || null,
    }));

    const payload = {
      medicineName: medicineName,
      activeIngredient: activeIngredient,
      quantity: quantity,
      medicationDetails: medicationDetails,
      strength: strength,
      frequencyTypeId: frequencyTypeId?.id,
      intakeTime: utcDateTime.intakeTime,
      alertBefore: reminder?.id || 0,
      fromDate: utcDateTime.fromDate,
      toDate: toDate ? dayjs(toDate).endOf("day") : null,
      customScheduleDetails: customSchedulePayload,
    };

    let response;

    if (typeof id === "number") {
      response = await dispatch(
        updateMedicationDetails({ ...payload, Id: id })
      );
      if (updateMedicationDetails.fulfilled.match(response)) {
        dispatch(fetchMedicationTasks());
        handleMedicationSummary(response?.payload);
      }
    } else {
      response = await dispatch(createMedicationDetails(payload));

      if (createMedicationDetails.fulfilled.match(response)) {
        dispatch(fetchMedicationTasks());
        handleMedicationSummary(response?.payload);
        setAnalyticsEvent(analyticsEventType.custom, {
          event: "tasks_medication_task_added",
          item_id: "tasks_medication_task_added",
          action: "User added medication task",
        });
      }
    }
    setIsCustomFilled(false);
  }

  async function handleForSubmitForm(data) {
    const disableOnMedicationLoading =
      status.updateMedicationDetails === TaskStatus.LOADING ||
      status.createMedicationStatus === TaskStatus.LOADING;
    const disableOnFormulaLoading =
      status.updateMedicationDetails === TaskStatus.LOADING ||
      status.createFormulaTaskStatus === TaskStatus.LOADING;
    if (disableOnMedicationLoading || disableOnFormulaLoading) {
      return;
    }
    const isCustom = Number(data?.frequencyTypeId?.id) === 11;
    if (isCustom && !isCustomFilled && formulaFormStep > 3) {
      setIsCustomFilled(true);
      setFormulaFormStep(1);
      return;
    }

    const {
      id,
      intakeTime,
      toDate,
      fromDate,
      reminder,
      frequencyTypeId,
      customSchedule,
      formulaName,
      formulaUnit,
      quantity,
    } = data || {};

    const modifiedFormulaName = formulaName
      .toLowerCase()
      .replace(/^\w/, (c: string) => c.toUpperCase());

    const customSchedulePayload = isCustom
      ? {
          interval: customSchedule?.interval?.id || 1,
          frequencyTypeId: customSchedule?.frequency?.id,
          monthDates: customSchedule?.monthDates?.length
            ? customSchedule?.monthDates
            : null,
          months: customSchedule?.months?.length
            ? customSchedule?.months
            : null,
          weekDays: customSchedule?.weekDays?.length
            ? customSchedule?.weekDays
            : null,
          weekIndex: customSchedule?.weekIndex ?? null,
        }
      : null;

    const utcDateTime = combineDateAndTimeToUTC(fromDate, intakeTime);
    const payload = {
      formulaName: modifiedFormulaName,
      quantity: quantity,
      frequencyTypeId: frequencyTypeId?.id,
      intakeTime: utcDateTime.intakeTime,
      alertBefore: reminder?.id || 0,
      fromDate: utcDateTime.fromDate,
      toDate: toDate ? dayjs(toDate).endOf("day") : null,
      customScheduleDetails: customSchedulePayload,
      unitId: formulaUnitList?.find((x) => x.id === formulaUnit.id)?.id || 1,
    };

    let response;
    if (typeof id === "number") {
      payload.Id = id;
      response = await dispatch(updateFormulaDetails(payload));
      if (updateFormulaDetails.fulfilled.match(response)) {
        dispatch(fetchFormulaTasks());
        handleFormulaSummary(response?.payload);
      }
    } else {
      response = await dispatch(createFormulaDetails(payload));
      if (createFormulaDetails.fulfilled.match(response)) {
        dispatch(fetchFormulaTasks());
        handleFormulaSummary(response?.payload);
        setAnalyticsEvent(analyticsEventType.custom, {
          event: "tasks_formula_task_added",
          item_id: "tasks_formula_task_added",
          action: "User added formula task",
        });
      }
    }
  }

  function getFooterButtonText(stepNumber: number) {
    switch (stepNumber) {
      case 1:
        return "Next";
      case 2:
        return "Schedule";
      case 3:
        return "Save";
      case 4:
        return "Next";
      default:
        return "Save";
    }
  }

  function getFormulaFooterButtonText(stepNumber: number) {
    switch (stepNumber) {
      case 1:
        return "Schedule";
      case 2:
        return "Save";
      case 3:
        return "Next";
      default:
        return "Save";
    }
  }

  function handleFormulaModalBackPress() {
    if (typeof selectedTaskId === "number" && formulaFormStep === 3) {
      setFormulaFormStep(1);
      return;
    }

    switch (formulaFormStep) {
      case 1: {
        typeof selectedTaskId === "number" ? setFormulaFormStep(3) : undefined;
        break;
      }
      case 2: {
        setFormulaFormStep(1);
        return;
      }
      case 3: {
        setFormulaFormStep(1);
        break;
      }
      case 4: {
        setFormulaFormStep(2);
        break;
      }
    }
  }

  async function handleFormulaModalNextPress() {
    const isStepValid = await triggerFormulaFormFields(
      FORMULA_FORM_VALIDATION_SCHEMA[formulaFormStep - 1].fields
    );
    if (isStepValid) {
      switch (formulaFormStep) {
        case 1: {
          setFormulaFormStep(2);
          return;
        }
        case 2:
          setFormulaFormStep(3);
        case 3:
          setFormulaFormStep(1);
      }
    }
  }

  function getFooterButtonPress(stepNumber: number) {
    switch (stepNumber) {
      case 1:
        return () => handleMedModalNextPress();
      case 2: {
        // setAnalyticsEvent(analyticsEventType.custom, {
        //   event: "tasks_medication_task_details_step_complete",
        //   item_id: "tasks_medication_task_details_step_complete",
        //   action: "User completed medication details step",
        // });
        return () => handleMedModalNextPress();
      }
      case 3: {
        // Final step - save medication task
        return handleMedicationSubmit(handleMedSubmitForm);
      }
      case 4:
        return () => handleMedModalBackPress();
      default:
        return () => {};
    }
  }

  function getFormulaFooterButtonPress(stepNumber: number) {
    switch (stepNumber) {
      case 1:
        return () => handleFormulaModalNextPress();
      case 2:
        return handleFormulaSubmit(handleForSubmitForm);
      case 3:
        return () => handleFormulaModalBackPress();
      case 4:
        return () => setFormulaFormStep(2);
      default:
        return () => {};
    }
  }

  function populateMedicationDetails(data, frequencies) {
    const setFields = [
      ["id", data?.id],
      ["medicineName", data?.medicineName],
      ["doses", data?.medicationDetails],
      ["activeIngredient", data?.activeIngredient],
      ["intakeTime", dayjs.utc(data?.fromDate).local()], //timeStamp
      ["fromDate", data?.fromDate],
      ["toDate", data?.toDate],
      ["frequencyTypeId", data?.frequencyTypeId],
      ["reminder", data?.alertBefore || 0],
      ["fromUpdate", true],
    ].map((field) => {
      setMedicationValue(field[0], field[1]);
    });

    if (data?.customScheduleDetails) {
      const frequencyTypeName = frequencies.find(
        (frequency: { id: any }) =>
          data?.customScheduleDetails?.frequencyTypeId === frequency.id
      )?.text;
      const interval = MOCK_DATA.MEDICATION_DAILY_ROUTINE.find(
        (frequency: { id: any }) =>
          data?.customScheduleDetails?.interval === frequency.id
      );

      setMedicationValue("customSchedule", {
        frequency: {
          id: data?.customScheduleDetails?.frequencyTypeId,
          value: data?.customScheduleDetails?.frequencyTypeId,
          label: frequencyTypeName,
        },
        isEachSelected: true,
        interval: interval,
        monthDates: data?.customScheduleDetails?.monthDates ?? [],
        months: data?.customScheduleDetails?.months ?? [],
        weekDays: data?.customScheduleDetails?.weekDays ?? [],
        weekIndex: data?.customScheduleDetails?.weekIndex ?? null,
      });
    }
    setMedFormStep(2);
  }
  function populateFormulaDetails(data, frequencies) {
    const setFields = [
      ["formulaName", data?.formulaName],
      ["quantity", data?.quantity ? `${data?.quantity}` : undefined],
      ["formulaUnit", data?.formulaUnit],
      ["id", data?.id],
      ["intakeTime", dayjs.utc(data?.fromDate).local()],
      ["fromDate", data?.fromDate],
      ["toDate", data?.toDate],
      ["frequencyTypeId", data?.frequencyTypeId],
      ["reminder", data?.alertBefore || 0],
      ["fromUpdate", true],
    ].map((field) => {
      setFormulaValue(field[0], field[1], field[2]);
    });

    if (data?.customScheduleDetails) {
      const frequencyTypeName = frequencies.find((frequency: { id: any }) => {
        return data?.customScheduleDetails?.frequencyTypeId === frequency.id;
      })?.text;
      const interval = MOCK_DATA.MEDICATION_DAILY_ROUTINE.find(
        (frequency: { id: any }) =>
          data?.customScheduleDetails?.interval === frequency.id
      );

      setFormulaValue("customSchedule", {
        frequency: {
          id: data?.customScheduleDetails?.frequencyTypeId,
          value: data?.customScheduleDetails?.frequencyTypeId,
          label: frequencyTypeName,
        },
        isEachSelected: true,
        interval: interval,
        monthDates: data?.customScheduleDetails?.monthDates ?? [],
        months: data?.customScheduleDetails?.months ?? [],
        weekDays: data?.customScheduleDetails?.weekDays ?? [],
        weekIndex: data?.customScheduleDetails?.weekIndex ?? null,
      });
    }

    handleFormulaModalBackPress();
  }

  function customUpdateHandler(isFormula: boolean) {
    isFormula ? setFormulaFormStep(4) : setMedFormStep(4);
  }

  const handleAddDose = () => {
    const currentDoses =
      (getMedicationFormValues("doses") as any[] | undefined) ?? [];

    // Add new dose with clean state
    setMedicationValue(
      "doses",
      [
        ...currentDoses,
        {
          patientRoutineDetailId: null,
          categoryTypeId: undefined,
          quantity: "",
          strength: "",
        },
      ],
      {
        shouldValidate: false, // Don't validate on add
        shouldDirty: false, // Don't mark as dirty
        shouldTouch: false, // Don't mark as touched
      }
    );
  };

  const handleRemoveDose = (index: number) => {
    const currentDoses = getMedicationFormValues("doses") || [];

    // Only unregister the fields for the dose being removed
    const fieldsToUnregister = [
      `doses.${index}.patientRoutineDetailId`,
      `doses.${index}.categoryTypeId`,
      `doses.${index}.quantity`,
      `doses.${index}.strength`,
    ];
    fieldsToUnregister.forEach((field) => unregister(field));

    // Create updated doses array excluding the removed dose
    const updatedDoses = currentDoses
      .filter((_, i) => i !== index)
      .map((dose, newIndex) => {
        // Re-register fields at their new indices
        setMedicationValue(
          `doses.${newIndex}.patientRoutineDetailId`,
          dose.patientRoutineDetailId || null
        );
        setMedicationValue(
          `doses.${newIndex}.categoryTypeId`,
          dose.categoryTypeId
        );
        setMedicationValue(`doses.${newIndex}.quantity`, dose.quantity || "");
        setMedicationValue(`doses.${newIndex}.strength`, dose.strength || "");

        return {
          patientRoutineDetailId: dose.patientRoutineDetailId || null,
          categoryTypeId: dose.categoryTypeId,
          quantity: dose.quantity || "",
          strength: dose.strength || "",
        };
      });

    // Update the doses array
    setMedicationValue("doses", updatedDoses, {
      shouldValidate: false,
      shouldDirty: true,
      shouldTouch: false,
    });

    // Trigger validation for remaining fields
    setTimeout(() => {
      updatedDoses.forEach((_, i) => {
        triggerMedFormFields([
          `doses.${i}.categoryTypeId`,
          `doses.${i}.quantity`,
          `doses.${i}.strength`,
        ]);
      });
    }, 0);
  };

  return {
    firstName,
    handleAddFormula,
    handleAddMedication,
    handleMedicationSummary,
    handleFormulaSummary,
    medModalRef,
    formulaModalRef,
    medFormStep,
    formulaFormStep,
    selectedTaskId,
    handleMedModalBackPress,
    handleFormulaModalBackPress,
    handleMedModalNextPress,
    handleFormulaModalNextPress,
    medicationFormControl,
    formulaFormControl,
    setFormulaValue,
    handleMedSubmitForm,
    getFooterButtonText,
    getFormulaFooterButtonText,
    getFormulaFooterButtonPress,
    getFooterButtonPress,
    handleMedModalClosePress,
    handleFormulaModalClosePress,
    getFrequencyName,
    populateMedicationDetails,
    populateFormulaDetails,
    customUpdateHandler,
    handleAddDose,
    handleRemoveDose,
  };
};

export default useTaskManagerContainer;
