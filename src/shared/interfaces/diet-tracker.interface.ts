interface Nutrient {
	name: string;
	amount: number;
	unit: string;
}

interface Portion {
	name: string;
	quantity: number;
	gram_weight: number;
	sequence_number: number;
}

interface FoodByIdResponse {
	_id: string;
	description: string;
	original_description: string;
	quantity: number;
	unit: string;
	gram_weight: number;
	protein: number;
	phe: number;
	nutrients: Nutrient[];
	portions: Portion[];
	factors: Factors;
	isFreeFood: boolean;
	user_flags?: {
		is_free?: boolean
	}
}

interface Factors {
	pheFactor: number;
	proteinFactor: number;
}

interface onFoodFormulaChangeResponse {
	quantity: number;
	protein: number;
	phe: number;
}

interface FoodSearchResponse {
	id: string;
	description: string;
	pheText: string;
	phe: string;
	protein: string;
	unit: string;
	quantity: number;
}

interface ListEntriesResponse {
	_id: string;
	time: string;
	client_id: string;
	user_id: string;
	description: string;
	items: EntryFoodDetail[];
}

interface EntryFoodDetail {
	description: string;
	quantity: number;
	unit: string;
	gram_weight?: number;
	phe: string;
	protein: string;
	food_id?: string;
	food_details?: Food;
	user_flags?: {
		is_free?: boolean;
	};
}

interface Food {
	_id: string;
	client_id: string;
	user_id: string;
	description: string;
	original_description: string;
	category: string;
	provider: string;
	quantity: number;
	unit: string;
	time: string;
	nutrients: Nutrient[];
	vector: number[];
	portions: Portion[];
	ingredients: Ingredient[];
	default_portion_phe: number | null;
	default_portion_protein: number | null;
}

interface Ingredient {
	description: string;
	category: string;
	quantity: number;
	unit: string;
	nutrients: Nutrient[];
	user_flags?: {
		is_free?: boolean;
	};
}

interface FoodDataState {
	finalProtein: string;
	finalPhe: string;
	finalQuantity: string;
	pheFactor: number;
	proteinFactor: number;
	gramWeight: number;
	selectedUnit: string;
	isFreeFood: boolean;
	isMeal?: boolean;	
}