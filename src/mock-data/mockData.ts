import { MedicationFormula } from '@/components/molecules/FormulaCard/FormulaCard';
import { Medication } from '@/components/molecules/MedicationCard/MedicationCard';
import Icons from '@/theme/assets/images/svgs/icons';

const MEDICATION_LISTING: Medication[] = [
  {
    medicineName: 'BoneCAl Plus',
    activeIngredient:
      'Calcium carbonate 750 mg. containing calcium 300 mg., Vitamin D 31 mg.',
    intakeTime: '13:20:00',
    fromDate: '2025-01-07T09:07:07.977',
    toDate: '2025-01-07T09:07:07.977',
    iconId: 4,
    period: 'Evening',
    upcoming: '2025-01-07T09:07:07.977',
    id: 17
  },
  {
    medicineName: 'nuberol forte',
    activeIngredient: 'paracetamol 650 mg + orphenadrine 50 mg',
    intakeTime: '12:20:00',
    fromDate: '2025-01-10T11:49:40.547',
    toDate: '2025-01-10T11:49:40.547',
    iconId: 2,
    period: 'Afternoon',
    upcoming: '2025-01-10T11:49:40.547',
    id: 13
  },
  {
    medicineName: 'nuberol forte',
    activeIngredient: 'paracetamol 650 mg + orphenadrine 50 mg',
    intakeTime: '12:20:00',
    fromDate: '2025-01-06T11:49:40.547',
    toDate: '2025-01-10T11:49:40.547',
    iconId: 6,
    period: 'Afternoon',
    upcoming: '2025-01-06T12:20:00',
    id: 11
  },
  {
    medicineName: 'BoneCAl Plus-Daily-Custom',
    activeIngredient: 'BoneCAl Plus-Daily-Custom',
    intakeTime: '13:20:00',
    fromDate: '2025-01-07T09:07:07.977',
    toDate: '2025-01-09T09:07:07.977',
    iconId: 0,
    period: 'Afternoon',
    upcoming: '2025-01-09T09:07:07.977',
    id: 25
  }
];

const FORMULA_LISTING: MedicationFormula[] = [
  {
    id: 1,
    date: '11-23-2025',
    time: '11:00 PM',
    dayTime: 'Monday'
  },
  {
    id: 2,
    date: '02-23-2025',
    time: '11:00 AM',
    dayTime: 'Evening'
  }
];

const MEDICATION_CATEGORIES = [
  {
    id: 1,
    value: 'tablet',
    label: 'Tablet'
  },
  {
    id: 2,
    value: 'powder',
    label: 'Powder'
  },
  {
    id: 3,
    value: 'injection',
    label: 'Injection'
  }
];

const MEDICATION_FREQUENCIES = [
  {
    id: 1,
    value: 'Day',
    label: 'Daily'
  },
  {
    id: 2,
    value: 'Week',
    label: 'Weekly'
  },
  {
    id: 3,
    value: 'Month',
    label: 'Monthly'
  },
  {
    id: 4,
    value: 'Year',
    label: 'Yearly'
  }
];

const MEDICATION_DAILY_ROUTINE = Array.from({ length: 999 }, (_, i) => ({
  id: i + 1,
  value: `${i + 1}${(n => n % 100 >= 11 && n % 100 <= 13 ? 'th' : { 1: 'st', 2: 'nd', 3: 'rd' }[n % 10] || 'th')(i + 1)}`,
  label: `${i + 1}`
}));

const MEDICATION_WEEKLY_ROUTINE = [
  {
    id: 0,
    value: 'sunday',
    label: 'Sunday'
  },
  {
    id: 1,
    value: 'monday',
    label: 'Monday'
  },
  {
    id: 2,
    value: 'tuesday',
    label: 'Tuesday'
  },
  {
    id: 3,
    value: 'wednesday',
    label: 'Wednesday'
  },
  {
    id: 4,
    value: 'thursday',
    label: 'Thursday'
  },
  {
    id: 5,
    value: 'friday',
    label: 'Friday'
  },
  {
    id: 6,
    value: 'saturday',
    label: 'Saturday'
  }
];

const MEDICATION_YEARLY_ROUTINE = [
  {
    id: 1,
    value: 'january',
    label: 'Jan'
  },
  {
    id: 2,
    value: 'february',
    label: 'Feb'
  },
  {
    id: 3,
    value: 'march',
    label: 'Mar'
  },
  {
    id: 4,
    value: 'april',
    label: 'Apr'
  },
  {
    id: 5,
    value: 'may',
    label: 'May'
  },
  {
    id: 6,
    value: 'june',
    label: 'Jun'
  },
  {
    id: 7,
    value: 'july',
    label: 'Jul'
  },
  {
    id: 8,
    value: 'august',
    label: 'Aug'
  },
  {
    id: 9,
    value: 'september',
    label: 'Sep'
  },
  {
    id: 10,
    value: 'october',
    label: 'Oct'
  },
  {
    id: 11,
    value: 'november',
    label: 'Nov'
  },
  {
    id: 12,
    value: 'december',
    label: 'Dec'
  }
];

const MEDICATION_REMINDER = [
  {
    id: 1,
    value: '5 mins',
    label: '5 mins'
  },
  {
    id: 2,
    value: '10 mins',
    label: '10 mins'
  },
  {
    id: 3,
    value: '20 mins',
    label: '20 mins'
  }
];

const PILL_SHAPES = [
  {
    id: 1,
    icon: Icons.PillShape1
  },
  {
    id: 2,
    icon: Icons.PillShape2
  },
  {
    id: 3,
    icon: Icons.PillShape3
  },
  {
    id: 4,
    icon: Icons.PillShape4
  },
  {
    id: 5,
    icon: Icons.PillShape5
  },
  {
    id: 6,
    icon: Icons.PillShape6
  },
  {
    id: 7,
    icon: Icons.PillShape7
  },
  {
    id: 8,
    icon: Icons.PillShape8
  },
  {
    id: 9,
    icon: Icons.PillShape9
  },
  {
    id: 10,
    icon: Icons.PillShape10
  }
];

const CUSTOM_CALENDAR_WEEKS = [
  {
    id: 1,
    value: 'first',
    label: 'First'
  },
  {
    id: 2,
    value: 'second',
    label: 'Second'
  },
  {
    id: 3,
    value: 'third',
    label: 'Third'
  },
  {
    id: 4,
    value: 'fourth',
    label: 'Fourth'
  },
  {
    id: 5,
    value: 'last',
    label: 'Last'
  }
];

const CUSTOM_CALENDAR_WEEKS_DAYS = [
  {
    id: 1,
    value: 'monday',
    label: 'Monday'
  },
  {
    id: 2,
    value: 'tuesday',
    label: 'Tuesday'
  },
  {
    id: 3,
    value: 'wednesday',
    label: 'Wednesday'
  },
  {
    id: 4,
    value: 'thursday',
    label: 'Thursday'
  },
  {
    id: 5,
    value: 'friday',
    label: 'Friday'
  },
  {
    id: 6,
    value: 'saturday',
    label: 'Saturday'
  },
  {
    id: 7,
    value: 'sunday',
    label: 'Sunday'
  },
  {
    id: 8,
    value: 'weekday',
    label: 'Weekday'
  },
  {
    id: 9,
    value: 'weekend',
    label: 'Weekend'
  }
];

export const MOCK_DATA = {
  MEDICATION_LISTING,
  FORMULA_LISTING,
  MEDICATION_CATEGORIES,
  PILL_SHAPES,
  MEDICATION_FREQUENCIES,
  MEDICATION_REMINDER,
  MEDICATION_DAILY_ROUTINE,
  MEDICATION_WEEKLY_ROUTINE,
  MEDICATION_YEARLY_ROUTINE,
  CUSTOM_CALENDAR_WEEKS,
  CUSTOM_CALENDAR_WEEKS_DAYS
};
