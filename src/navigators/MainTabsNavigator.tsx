import { vs } from "react-native-size-matters";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { getFocusedRouteNameFromRoute } from "@react-navigation/native";
import Common from "@/theme/common.style";
import { Typography } from "@/components/atoms";
import { DietTracker } from "@/screens";
import { getIconComponent, TabNames } from "@/constants/screens";
import LabsStacks from "./stacks/LabStacks";
import TaskManagerScreen from "@/screens/App/TaskManager/TaskManagerScreen";
import { useTheme } from "@/theme";
import HomeStack from "./stacks/HomeStack";

const Tab = createBottomTabNavigator();

const isTabName = (name: string): name is TabNames => {
  return ["Home", "Labs", "Tasks", "Diet"].includes(name);
};

const MainTabsNavigator = () => {
  const { colors } = useTheme();

  const getTabBarColors = (route) => {
    const routeName = getFocusedRouteNameFromRoute(route) ?? "HomeScreen";

    if (route.name === "Home" && routeName === "PatientContactsStack") {
      // 👇 Same color for both, so no highlight
      return {
        active: colors.textPrimary,
        inactive: colors.textPrimary,
      };
    }

    return {
      active: colors.primary,
      inactive: colors.textPrimary,
    };
  };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => {
        const { active, inactive } = getTabBarColors(route);
        return {
          headerShown: false,
          unmountOnBlur: true,
          tabBarStyle: Common.tabBarStyle,
          tabBarItemStyle: Common.tabBarItemStyle,
          tabBarActiveTintColor: active,
          tabBarInactiveTintColor: inactive,
          tabBarIcon: ({ focused, color }) => {
            if (!isTabName(route.name)) return null;
            const TabIcon = getIconComponent(route.name);
            return <TabIcon color={color} />;
          },
          tabBarLabel: ({ color }) => (
            <Typography.B5 style={{ color, marginTop: vs(7.2) }}>
              {route.name}
            </Typography.B5>
          ),
        };
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeStack}
        listeners={({ navigation }) => ({
          tabPress: () => {
            navigation.navigate("Home", {
              screen: "HomeScreen",
            });
          },
        })}
      />
      <Tab.Screen name="Tasks" component={TaskManagerScreen} />
      <Tab.Screen name="Labs" component={LabsStacks} />
      <Tab.Screen name="Diet" component={DietTracker} />
    </Tab.Navigator>
  );
};

export default MainTabsNavigator;
