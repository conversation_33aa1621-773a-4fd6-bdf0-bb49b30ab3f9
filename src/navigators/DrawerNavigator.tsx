import React from "react";
import { createDrawerNavigator } from "@react-navigation/drawer";
import MainTabsNavigator from "./MainTabsNavigator";
import ProfileStacks from "./stacks/ProfileStacks";
import TutorialsStack from "./stacks/TutorialsStack";
import CustomDrawerContent from "@/components/template/CustomDrawerContent/CustomDrawerContent";
import { PatientContacts } from "@/screens";

const Drawer = createDrawerNavigator();

const DrawerNavigator = () => (
  <Drawer.Navigator
    drawerContent={(props) => <CustomDrawerContent {...props} />}
    screenOptions={{
      drawerPosition: "right",
      headerShown: false,
      drawerStyle: {
        width: "100%", // Makes the drawer full screen
      },
    }}
  >
    <Drawer.Screen name="MainTabs" component={MainTabsNavigator} />
    <Drawer.Screen
      name="UserProfile"
      component={ProfileStacks}
      options={{ unmountOnBlur: true }}
    />
    <Drawer.Screen
      name="PatientContacts"
      component={PatientContacts}
      options={{ unmountOnBlur: true }}
    />
    <Drawer.Screen
      name="HowToVideos"
      component={TutorialsStack}
      options={{ unmountOnBlur: true }}
    />
  </Drawer.Navigator>
);

export default DrawerNavigator;
