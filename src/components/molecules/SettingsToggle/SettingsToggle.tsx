import React from "react";
import {
  View,
  Switch,
  Pressable,
  ActivityIndicator,
} from "react-native";

import { Typography } from "@/components/atoms";
import { config } from "@/theme/_config";
import getToggleStyles from "./SettingsToggle.styles";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useTheme } from "@/theme";
import { useTranslation } from "react-i18next";

interface SettingsToggleProps {
  title: string;
  onToggle?: (value: boolean) => void;
  isToggleOn?: boolean;
  disabled?: boolean;
}

const SettingsToggle: React.FC<SettingsToggleProps> = ({
  title,
  onToggle,
  isToggleOn,
  disabled,
}) => {
  const styles: any = useDynamicStyles(getToggleStyles);
  const { toggleLoading, toggleNotify } = useSelector(
    (state: RootState) => state.notification
  );

  const { t } = useTranslation(["pushnotification"]);
  const { colors } = useTheme();

  const toggleSwitch = () => {
    onToggle?.(!isToggleOn);
  };

  return (
    <Pressable disabled={disabled}>
      <View style={styles.settingsOption}>
        <View style={styles.mainHeadingContainer}>
          <Typography.B1 style={styles.settingsOptionText}>
            {title}
          </Typography.B1>
          <View style={styles.toggle}>
            {toggleLoading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Switch
                trackColor={{ false: "#767577", true: config.colors.primary }}
                thumbColor={isToggleOn ? "#f4f3f4" : "#f4f3f4"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={toggleSwitch}
                value={isToggleOn}
                disabled={disabled}
              />
            )}
          </View>
        </View>

        <View style={styles.descriptionContainer}>
          <Typography.B2 style={styles.disclaimer}>
            {t("pushnotification.description")}
          </Typography.B2>
        </View>
      </View>
    </Pressable>
  );
};

export default SettingsToggle;
