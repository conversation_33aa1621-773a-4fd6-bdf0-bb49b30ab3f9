import React from "react";
import { View, TouchableOpacity } from "react-native";

import { Typography } from "@/components/atoms";
import Tag from "@/components/atoms/Tag/Tag";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getSettingsOptionStyle from "./SettingsOption.styles";
import { getPheAllowanceUnit, getPheAllowanceValue } from "@/utils/helpers";

interface SettingsOptionProps {
  title: string;
  tagText?: string;
  icon?: React.ReactNode; // Accepts any React node as an icon prop
  onPress?: () => void; // Optional onPress function
  disabled?: boolean;
  consumptionType?: "Phe" | "Protein";
  tagInfo?: { amount: number; unit: string };
}

const SettingsOption: React.FC<SettingsOptionProps> = ({
  title,
  tagText,
  icon,
  onPress,
  disabled,
  consumptionType = "Phe",
  tagInfo,
}) => {
  const styles: any = useDynamicStyles(getSettingsOptionStyle);
  return (
    <TouchableOpacity onPress={onPress} disabled={disabled}>
      <View style={styles.settingsOption}>
        {/* Title Row */}
        <View style={styles.row}>
          <Typography.B1 style={styles.settingsOptionText}>
            {title}
          </Typography.B1>

          {tagInfo && (
            <Tag.Main style={styles.tagView}>
              <Typography.B5 style={styles.tagText}>
                {tagText
                  ? tagText
                  : `${getPheAllowanceValue(Number(tagInfo?.amount || 0), consumptionType || "Phe")} ${getPheAllowanceUnit(consumptionType || "Phe")}`}
              </Typography.B5>
            </Tag.Main>
          )}

          {!icon ? null : <View>{icon}</View>}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SettingsOption;
