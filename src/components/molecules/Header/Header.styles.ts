import { Fonts } from "@/constants";
import { ms, ScaledSheet, vs } from "react-native-size-matters";

const getHeaderStyle = theme => ScaledSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: vs(12),
  },
  backButton: {
    marginRight: ms(8),
  },
  hamburgerButton: {
    paddingLeft: ms(30),
    width: ms(50),
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
  },
  titleLeftContainer: {
    justifyContent: "flex-start",
    alignItems: "flex-start",
  },
  headerTitle: {
    fontFamily: Fonts.RALEWAY_BOLD,
    fontSize: ms(18),
    textAlign: "center",
  },
  headerTitleLeft: {
    textAlign: "left",
    marginLeft: 0,
  },
  headerTitleCenter: {
    textAlign: "center",
    marginLeft: ms(50),
  },
  emptyRightContainer: {
    marginLeft: ms(-45),
    width: ms(80),
  },
  editButton: {
    width: ms(79),
    borderWidth: ms(1),
    alignItems: "center",
    justifyContent: "center",
    borderColor: theme.colors.gray,
  },
  iconTextWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  editText: {
    marginLeft: ms(4),
    fontFamily: Fonts.RALEWAY_BOLD,
    textAlign: "center",
    marginBottom: ms(2),
  },
  isEditingTextSpace: {
    marginRight: ms(70),
  },
});


export default getHeaderStyle