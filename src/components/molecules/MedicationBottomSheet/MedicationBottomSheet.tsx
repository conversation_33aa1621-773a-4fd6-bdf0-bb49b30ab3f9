import { useTranslation } from "react-i18next";
import { <PERSON>ton, Typography } from "@/components/atoms";
import { ScrollView } from "react-native-gesture-handler";
import { TouchableWithoutFeedback, View } from "react-native";
import React, {
  FC,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import Loading from "@/components/atoms/Loading/Loading";

import Step2 from "./Step2";
import Step3 from "./Step3";
import Step4 from "./Step4";
import Step5 from "./Step5";
import { useTheme } from "@/theme";
import RNBottomSheetWrapper, {
  RNBottomSheetWrapperProps,
} from "@/components/atoms/RNBottomSheetWrapper/RNBottomSheetWrapper";
import { useAppSelector } from "@/store";
import { MOCK_DATA } from "@/mock-data/mockData";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { getMedicationSheetStyles } from "./MedicationBottomSheet.style";
import { selectTask } from "@/store/slices/taskManager/taskManager.slice";

export interface MedicationBottomSheetProp
  extends Partial<RNBottomSheetWrapperProps> {}

const MedicationBottomSheet: FC<MedicationBottomSheetProp> = forwardRef(
  (
    {
      medFormStep,
      getFrequencyName,
      populateMedicationDetails,
      handleMedModalBackPress,
      control,
      onDeletePress,
      selectedTaskId,
      getFooterButtonText,
      getFooterButtonPress,
      handleMedModalClosePress,
      customUpdateHandler,
      isLoading,
      handleAddDose,
      handleRemoveDose,
    },
    ref
  ) => {
    const { colors } = useTheme();
    const bottomSheetModalRef = useRef(null);
    const styles: any = useDynamicStyles(getMedicationSheetStyles);
    const { t } = useTranslation(["taskManager"]);
    const [loading, setLoading] = useState(false);

    const { taskDetails, frequencies, reminderTimes, categories } =
      useAppSelector(selectTask);
    const frequencyTypeName = frequencies.find(
      (frequency) => taskDetails?.frequencyTypeId === frequency.id
    )?.text;
    const reminderTimeName = reminderTimes.find(
      (rt) => taskDetails?.alertBefore === rt.id
    )?.text;
    const categoryTypeName = categories.find(
      (category) => taskDetails?.categoryTypeId === category.medicationTypeId
    )?.name;

    const iconId = MOCK_DATA.PILL_SHAPES.find(
      (pill) => taskDetails?.iconId === pill.id
    );

    const openHandler = () => {
      bottomSheetModalRef.current?.present();
    };

    const closeHandler = () => {
      bottomSheetModalRef.current?.dismiss();
      handleMedModalClosePress();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: openHandler,
        close: closeHandler,
      }),
      []
    );

    const RenderStepComponent = () => {
      switch (medFormStep) {
        case 2:
          return (
            <Step2
              control={control}
              handleAddDose={handleAddDose}
              handleRemoveDose={handleRemoveDose}
            />
          );
        case 3:
          return (
            <Step3
              isFormula={false}
              control={control}
              getFrequencyName={getFrequencyName}
              customUpdateHandler={customUpdateHandler}
            />
          );
        case 4:
          return <Step4 control={control} />;
        case 5:
          return <Step5 control={control} isLoading={isLoading} />;
        default:
          return (
            <Step2
              control={control}
              handleAddDose={handleAddDose}
              handleRemoveDose={handleRemoveDose}
            />
          );
      }
    };

    const hideBackButton =
      (medFormStep == 2 && typeof selectedTaskId !== "number") ||
      medFormStep == 5;

    const handleFooterButtonPress = async () => {
      setLoading(true);
      try {
        const fn = getFooterButtonPress(medFormStep);
        if (typeof fn === "function") {
          await fn();
        }
      } finally {
        setLoading(false);
      }
    };

    return (
      <>
        <RNBottomSheetWrapper ref={bottomSheetModalRef} snapPoint={["92.5%"]}>
          <View style={styles.header}>
            {hideBackButton ? (
              <View />
            ) : (
              <TouchableWithoutFeedback onPress={handleMedModalBackPress}>
                <Typography.B1>{t("back")}</Typography.B1>
              </TouchableWithoutFeedback>
            )}
            <View />

            <TouchableWithoutFeedback onPress={closeHandler}>
              <Typography.B1>{t("close")}</Typography.B1>
            </TouchableWithoutFeedback>
          </View>

          <ScrollView
            contentContainerStyle={styles.container}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled
          >
            <View style={{ flex: 1 }}>{RenderStepComponent()}</View>

            {medFormStep <= 4 ? (
              <Button.Main
                style={styles.nextBtnContainer}
                onPress={handleFooterButtonPress}
              >
                <Typography.H5 style={styles.nextBtn}>
                  {getFooterButtonText(medFormStep)}
                </Typography.H5>
              </Button.Main>
            ) : (
              <View>
                <Button.YellowOutline
                  style={{
                    ...styles.editBtnContainer,
                    borderColor: colors.delete,
                  }}
                  onPress={onDeletePress}
                >
                  <Typography.H5
                    style={[styles.nextBtn, { color: colors.textPrimary }]}
                  >
                    Remove
                  </Typography.H5>
                </Button.YellowOutline>

                <Button.Main
                  style={styles.nextBtnContainer}
                  onPress={() => {
                    populateMedicationDetails(
                      {
                        ...taskDetails,
                        strength: `${taskDetails?.strength || 0}`,
                        quantity: `${taskDetails?.quantity || 0}`,
                        frequencyTypeId: {
                          label: frequencyTypeName,
                          id: taskDetails?.frequencyTypeId,
                          value: taskDetails?.frequencyTypeId,
                        },
                        alertBefore: {
                          label: reminderTimeName,
                          id: taskDetails?.alertBefore,
                          value: taskDetails?.alertBefore,
                        },
                        categoryTypeId: {
                          label: categoryTypeName,
                          id: taskDetails?.categoryTypeId,
                          value: taskDetails?.categoryTypeId,
                        },
                        iconId: iconId,
                      },
                      frequencies
                    );
                  }}
                >
                  <Typography.H5 style={styles.nextBtn}>Edit</Typography.H5>
                </Button.Main>
              </View>
            )}
          </ScrollView>
          {loading && <Loading />}
        </RNBottomSheetWrapper>
      </>
    );
  }
);

export default MedicationBottomSheet;
