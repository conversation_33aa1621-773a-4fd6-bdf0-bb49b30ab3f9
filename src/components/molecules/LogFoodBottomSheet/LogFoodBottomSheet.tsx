import { Loader } from "@/components/atoms";
import BottomSheetWrapper from "@/components/atoms/BottomSheetWrapper/BottomSheetWrapper";
import Button from "@/components/atoms/Button/Button";
import CustomCamera from "@/components/atoms/CustomCamera/CustomCamera"; // Import the CustomCamera component
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Typography from "@/components/atoms/Typography/Typography";
import ManualEntryFood from "@/components/molecules/ManualEntryFood/ManualEntryFood";
import MealItem from "@/components/molecules/MealItem/MealItem";
import { AiSuggestions } from "@/components/template";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  analyseFood,
  cleanApiState,
  clearScanData,
  clearSearch,
  createFoodEntry,
  fetchFoodEntries,
  searchFoodByFrequency,
  selectDietTracker,
  selectFoodItemsByFrequency,
} from "@/store/slices/dietTrackerSlice";
import Icons from "@/theme/assets/images/svgs/icons";
import Common from "@/theme/common.style";
import { FoodEntryItem } from "@/types/schemas/dietTracker";

interface FoodSearchResponse {
  id?: string;
  description?: string;
  quantity?: number;
  unit?: string;
  phe?: number;
  protein?: number;
  pheText?: string;
  user_flags?: {
    is_free?: boolean;
  };
}
import {
  compressImage,
  removeTrailingZeros,
  roundNumber,
  updateDateWithTimeSlot,
} from "@/utils/helpers";
import { PortionConverter } from "@/utils/portions";
import React, { useEffect, useState, useRef } from "react";
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  Linking,
  Platform,
  RefreshControl,
  TouchableOpacity,
  View,
} from "react-native";
import { useSelector } from "react-redux";

import { useTheme } from "@/theme";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { launchCamera } from "react-native-image-picker";
import { useCameraPermission } from "react-native-vision-camera";
import GenericModal from "../GenericModal/GenericModal";
import LogFoodInput from "../LogFoodInput/LogFoodInput";
import getLogFoodStyle from "./LogFoodBottomSheet.style";
import { getFrequentEntries } from "@/services/api/dietTrackerAPI";
import { config } from "@/theme/_config";
import {
  selectConsumptionUnit,
  selectIsSimplifiedDiet,
} from "@/store/slices/settingsSlice";

interface LogFoodBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  foodEntries?: Array<FoodEntryItem>;
  userName?: string;
}

const LogFoodBottomSheet: React.FC<LogFoodBottomSheetProps> = ({
  isVisible,
  onClose,
  userName,
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFood, setSelectedFood] = useState<FoodEntryItem | null>(null); // State to track the selected meal
  const [isManualEntry, setIsManualEntry] = useState(false); // State to show Manual Entry screen
  const [showCamera, setShowCamera] = useState(false); // State to toggle the camera modal
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null); // State for captured photo URI
  const [originalCapturedPhoto, setOriginalCapturedPhoto] = useState<string | null>(null); // State for captured photo URI
  const [isScanMeal, setIsScanMeal] = useState(false);
  const [frequentLoading, setFrequentLoading] = useState(false);
  const consumptionType = useSelector(selectConsumptionUnit);
  const isSimplifiedDiet = useAppSelector(selectIsSimplifiedDiet);

  const [frequentFood, setFrequentFood] = useState<FoodSearchResponse[] | null>(
    []
  );

  const searchEventFired = useRef<boolean>(false);

  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const { setAnalyticsEvent } = useAnalytics();

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getLogFoodStyle);

  const { requestPermission } = useCameraPermission();

  const dispatch = useAppDispatch();
  const searchResults = useSelector(selectFoodItemsByFrequency);

  const { selectedDietDate, selectedTimeSlot, scanStatus } =
    useAppSelector(selectDietTracker);

  useEffect(() => {
    const delay = 100;
    const minSearchLength = 2;

    const handler = setTimeout(() => {
      if (searchQuery.trim().length >= minSearchLength) {
        // Only fire the analytics event once per search session
        if (!searchEventFired.current) {
          setAnalyticsEvent(analyticsEventType.custom, {
            event: "diet_search_food_started",
            item_id: "diet_search_food_started",
            action: "User started search for food",
          });
          searchEventFired.current = true;
        }
        dispatch(searchFoodByFrequency(searchQuery));
      } else {
        dispatch(clearSearch()); // Explicitly clear results when query is empty
        // Reset the search event flag when the search query is cleared
        searchEventFired.current = false;
      }
    }, delay);

    return () => clearTimeout(handler);
  }, [searchQuery, dispatch]);

  useEffect(() => {
    getFreqFood();
    dispatch(clearScanData());
    return () => {
      dispatch(clearSearch());
      searchEventFired.current = false;
    };
  }, [dispatch, isSimplifiedDiet]);

  const getFreqFood = async () => {
    try {
      setFrequentLoading(true);
      const v = await getFrequentEntries(consumptionType);
      setFrequentFood(v as any);
    } catch (error) {
      console.error("Error fetching frequent foods:", error);
    } finally {
      setFrequentLoading(false);
    }
  };

  const handleSelectItem = (item: any) => {
    Keyboard.dismiss();
    setSelectedFood(item as FoodEntryItem);

    setAnalyticsEvent(analyticsEventType.custom, {
      event: "diet_search_food_logged",
      item_id: "diet_search_food_logged",
      action: "User logged searched food",
    });
  };

  const handleSearchTextChange = (text: string) => {
    // If the user clears the search box, reset the search event flag
    if (text.trim().length === 0) {
      searchEventFired.current = false;
    }
    setSearchQuery(text);
  };

  const renderMealItem = ({ item }: { item: FoodSearchResponse }) => {
    const valueToDisplay =
      isSimplifiedDiet && (item as any).user_flags?.is_free
        ? "FREE"
        : consumptionType === "Protein"
          ? `${Number(roundNumber(item.protein || 0)) || 0}`
          : `${Number(roundNumber(item.phe || 0)) || 0}`;

    item.isFreeFood = item?.user_flags?.is_free;

    return (
      <View>
        <MealItem
          key={item.id}
          name={item.description || "No description available"}
          quantity={`${removeTrailingZeros(item.quantity || 0)}`}
          unit={item.unit || ""}
          phe={valueToDisplay}
          icon={
            <View style={styles.icon}>
              <Icons.Plus width={10} height={10} color={colors.textPrimary} />
            </View>
          }
          onPress={() => setSelectedFood(item as unknown as FoodEntryItem)} // Type cast to FoodEntryItem
          backgroundColor={colors.dropDownGray} // Custom background color
          tagBackgroundColor={colors.tile_bg} // Custom tag background color
          textColor={colors.textPrimary} // Custom text color
          isTextBold
          isFreeFood={item?.user_flags?.is_free}
        />
      </View>
    );
  };

  const renderSearchItem = ({ item }: { item: any }) => {
    return (
      <TouchableOpacity
        style={styles.searchItem}
        onPress={() => handleSelectItem(item)} // Handle item selection
        activeOpacity={0.7} // Provide feedback
      >
        <View style={styles.searchItemRow}>
          {/* Description on the left */}
          <Typography.B2 style={[styles.itemName, Common.textBold]}>
            {item?.description}
          </Typography.B2>

          {/* Details in the center */}
          <Typography.B3
            style={styles.itemDetails}
          >{`${item?.pheText}`}</Typography.B3>
        </View>
      </TouchableOpacity>
    );
  };

  const onLogFood = async ({
    quantity,
    phe,
    unit,
    protein,
    gram_weight,
    isFreeFood,
  }: {
    quantity: number;
    phe: number;
    unit: string;
    protein: number;
    gram_weight: number;
    isFreeFood?: boolean;
  }) => {
    try {
      setAnalyticsEvent(analyticsEventType.custom, {
        event: "diet_custom_food_started",
        item_id: "diet_custom_food_started",
        action: "User tapped custom food",
      });

      const time = updateDateWithTimeSlot(
        `${selectedDietDate}`,
        selectedTimeSlot
      );
      const _oldGramWeight = PortionConverter.toGrams(
        unit || selectedFood?.unit,
        quantity || selectedFood?.quantity
      );
      const payload = {
        description: selectedFood?.description,
        time: time,
        items: [
          {
            description: selectedFood?.description,
            quantity: quantity || selectedFood?.quantity,
            unit: unit || selectedFood?.unit,
            gram_weight: gram_weight || _oldGramWeight,
            phe: phe || selectedFood?.phe,
            protein: protein || selectedFood?.protein,
            food_id: selectedFood?.id,
            user_flags: {
              is_free: isFreeFood,
            },
          },
        ],
      };

      const response = await dispatch(createFoodEntry(payload as any));
      const formattedDate = new Date(selectedDietDate);

      if (createFoodEntry.fulfilled.match(response)) {
        await dispatch(fetchFoodEntries({ date: formattedDate }));
        getFreqFood();
      }

      setSearchQuery("");
      setSelectedFood(null);
      onClose();
      dispatch(cleanApiState());

      setAnalyticsEvent(analyticsEventType.custom, {
        event: "diet_custom_food_logged",
        item_id: "diet_custom_food_logged",
        action: "User logged custom food",
      });
    } catch (e) {
      console.error("Error logging food:", e);
    }
  };

  const handlePhotoCaptured = async (uri: string) => {
    try {
      setOriginalCapturedPhoto(uri);
      // 1️⃣ Compress the captured image
      const compressedUri = await compressImage(uri);
      // 2️⃣ Prepare the FormData with the compressed image
      setCapturedPhoto(compressedUri);
      setIsScanMeal(true);

      const formData = new FormData();
      formData.append("file", {
        uri: compressedUri,
        name: "captured_image.jpg", // Replace with actual file name if available
        type: "image/jpeg",
      });

      setShowCamera(false);

      // 3️⃣ Dispatch the `analyseFood` thunk
      const response = await dispatch(
        analyseFood({
          formData: formData, // Your FormData instance
        })
      );

      if (analyseFood.fulfilled.match(response)) {
        setOriginalCapturedPhoto(null);
      }
    } catch (error) {
      console.error("❌ Failed to analyze food:", error);
      setShowCamera(false);
    }
  };

  const checkPermission = async () => {
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "diet_scan_meal_started",
      item_id: "diet_scan_meal_started",
      action: "User tapped scan meal",
    });

    const isGranted = await requestPermission();
    if (!isGranted) {
      setShowPermissionModal(true); // Show your generic modal
    } else if (Platform.OS === "android") {
      setShowCamera(true);
    } else if (Platform.OS === "ios") {
      const result = await launchCamera({
        mediaType: "photo",
        quality: 0.8,
        maxWidth: 1024,
        maxHeight: 1024,
        includeBase64: false,
        saveToPhotos: false,
      });
      if (result.didCancel) {
        setShowCamera(false);
        return;
      }
      handlePhotoCaptured(result?.assets?.[0]?.uri ?? "");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Your refresh logic here (e.g., fetch updated data)
      await fetchUpdatedFoodEntries();
    } catch (error) {
      console.error("Error refreshing food entries:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchUpdatedFoodEntries = async () => {
    try {
      // Dispatch the action to fetch food entries
      await getFreqFood();
    } catch (error) {
      console.error("Error fetching updated food entries:", error);
    }
  };

  return (
    <>
      {/* CustomCamera Modal */}
      <CustomCamera
        isVisible={showCamera}
        // isVisible={true}
        key={`camera-${showCamera}`}
        onClose={() => setShowCamera(false)}
        onPhotoCaptured={handlePhotoCaptured} // Pass the photo URI to LogFoodBottomSheet
      />

      <GenericModal
        isVisible={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        onConfirm={() => Linking.openSettings()} // Handle confirm button
        headerText="Camera Permission Required"
        bodyText="To access the camera, please grant permission in your device settings."
        confirmText="Enable"
        closeText="Cancel"
        customHeader={null} // Use default header
        customBody={null} // Use default body
        customFooter={null} // Use default footer
      />
      <BottomSheetWrapper
        isVisible={isVisible}
        onClose={() => {
          setSelectedFood(null);
          setIsManualEntry(false); // Close Manual Entry screen
          setIsScanMeal(false);
          setSearchQuery("");
          searchEventFired.current = false;

          if (selectedFood) {
            setSelectedFood(null);
          } else if (isManualEntry) {
            setIsManualEntry(false); // Close Manual Entry screen
          } else if (isScanMeal) {
            setIsScanMeal(false);
          } else {
            setSearchQuery("");
            onClose();
          }
        }}
        closeText={
          isScanMeal || selectedFood || isManualEntry ? "Cancel" : "Close"
        }
      >
        <View style={styles.header}>
          <Typography.H1 style={[Common.textBold, styles.title]}>
            {isScanMeal
              ? "Scan Meal"
              : selectedFood?.description
                ? selectedFood?.description
                : isManualEntry
                  ? "Add Manually"
                  : "Log Food"}
          </Typography.H1>
        </View>

        {isManualEntry ? (
          <ManualEntryFood
            onClose={() => {
              onClose();
              setIsManualEntry(false);
            }}
          />
        ) : isScanMeal ? (
          <AiSuggestions
            photoUri={capturedPhoto}
            onClose={() => {
              onClose();
              setIsScanMeal(false);
            }}
          />
        ) : selectedFood ? (
          <LogFoodInput
            onLogFood={onLogFood}
            selectedFood={selectedFood} // Pass captured photo URI
            photoUri={capturedPhoto}
            isScanMeal={isScanMeal}
          />
        ) : (
          <View>
            {/* Search Input */}
            <Typography.B1>Search</Typography.B1>
            <View style={styles.relative_position}>
              <CustomTextInput
                placeholder="Search for Foods"
                value={searchQuery}
                onChangeText={handleSearchTextChange} // Trigger search on text change
                icon={<Icons.Search />}
              />
              {/* {loading && <Typography.B1>Loading...</Typography.B1>} */}
              {searchQuery.length > 2 && searchResults?.length ? (
                <View style={styles.searchResultsView}>
                  <FlatList
                    data={searchResults}
                    keyExtractor={(item, index) =>
                      `${item.description}-${index}`
                    }
                    renderItem={renderSearchItem}
                    style={styles.searchResultsList}
                    keyboardShouldPersistTaps="handled" // Allow taps without dismissing the keyboard
                  />
                  <TouchableOpacity onPress={() => setIsManualEntry(true)}>
                    <View style={styles.searchAddItemView}>
                      <Typography.B2 style={Common.textBold}>
                        Add new{" "}
                      </Typography.B2>
                      <Icons.CirclePlus width={15} height={15} />
                    </View>
                  </TouchableOpacity>
                </View>
              ) : null}
            </View>

            <View style={styles.reverse_zIndex_1}>
              {/* Action Buttons */}
              <View style={styles.actionButtonsContainer}>
                <Button.Main
                  style={styles.actionButton}
                  onPress={checkPermission} // Open CustomCamera
                >
                  <Icons.Scan width={37} height={37} />
                  <Typography.H4 style={styles.actionButtonText}>
                    Scan Meal
                  </Typography.H4>
                </Button.Main>
                <Button.Main
                  style={[styles.manualEntryColor, styles.actionButton]}
                  onPress={() => setIsManualEntry(true)} // Show Manual Entry screen
                >
                  <Icons.CirclePlus
                    width={37}
                    height={37}
                    stroke={colors.textPrimary}
                  />
                  <Typography.H4
                    style={[
                      styles.actionButtonText,
                      { color: colors.textPrimary },
                    ]}
                  >
                    Manual Entry
                  </Typography.H4>
                </Button.Main>
              </View>

              {/* Food List */}
              <Typography.H1 style={[Common.textBold, styles.foodsLabel]}>
                {userName ? `${userName}'s meals` : "Your meal(s)"}
              </Typography.H1>
              <View style={styles.separator} />
              {frequentLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              ) : (
                <FlatList
                  ListEmptyComponent={() => {
                    return (
                      <View style={{ marginVertical: "3%" }}>
                        <Typography.H4
                          style={{
                            ...styles.actionButtonText,
                            color: colors.textPrimary,
                          }}
                        >
                          The foods you eat the most often will appear here
                        </Typography.H4>
                      </View>
                    );
                  }}
                  data={frequentFood}
                  renderItem={renderMealItem} // Render each FoodEntry
                  keyExtractor={(item, index) =>
                    (item.id ?? index).toString() + index
                  } // Unique key for each FoodEntry
                  style={styles.foodListView}
                  ItemSeparatorComponent={<View style={styles.gap_8} />}
                  contentContainerStyle={styles.foodList}
                  showsVerticalScrollIndicator
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={onRefresh}
                      tintColor={config.colors.primary}
                      colors={[config.colors.primary]}
                    />
                  }
                />
              )}
            </View>
          </View>
        )}
        {scanStatus === "loading" ? (
          <Loader.ScanMeal photoUri={originalCapturedPhoto} />
        ) :
          null}
      </BottomSheetWrapper>
    </>
  );
};

export default LogFoodBottomSheet;
