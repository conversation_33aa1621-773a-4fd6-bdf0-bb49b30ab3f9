import Button from "@/components/atoms/Button/Button";
import CustomTextInput from "@/components/atoms/CustomTextInput/CustomTextInput";
import Dropdown from "@/components/atoms/Dropdown/Dropdown";
import TabSelector from "@/components/atoms/TabSelector/TabSelector";
import Typography from "@/components/atoms/Typography/Typography";
import React, { useCallback, useEffect, useState } from "react";
import { KeyboardTypeOptions, View } from "react-native";

import { useTheme } from "@/theme";
import { config } from "@/theme/_config";
import Common from "@/theme/common.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { Portion } from "@/types/schemas/dietTracker";
import { DietTrackerService } from "@/utils/dietTrackerService";
import { validateNonZeroNumericInput } from "@/utils/helpers";
import getAISuggestionItemStyles from "./AISuggestionsItem.styles";
import RNCheckbox from "@/components/atoms/RNCheckbox/RNCheckbox";

interface AISuggestionsItemProps {
  item: any; // Replace `any` with your specific type
  onRemove: () => void;
  onUpdate?: (updatedFields: any) => void;
}

const AISuggestionsItem: React.FC<AISuggestionsItemProps> = ({
  item,
  onRemove,
  onUpdate,
}) => {
  const [selectedTab, setSelectedTab] = useState("Quantity");
  const [selectedUnit, setSelectedUnit] = useState<string>(item.unit);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [portions, setPortions] = useState([]);
  const [finalProtein, setFinalProtein] = useState("");
  const [finalPhe, setFinalPhe] = useState("");
  const [finalQuantity, setFinalQuantity] = useState("");
  const [pheFactor, setPheFactor] = useState(0);
  const [proteinFactor, setProteinFactor] = useState(0);
  const [gramWeight, setGramWeight] = useState(0);
  const [quantityError, setQuantityError] = useState<string | null>(null);

  const freeFoodText = "FREE";

  const { colors } = useTheme();
  const styles: any = useDynamicStyles(getAISuggestionItemStyles);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  useEffect(() => {
    let mounted = true;
    if (mounted) {
      if (item) {
        let phe_Factor = 0;
        let protein_Factor = 0;
        if (item.factors) {
          phe_Factor = item.factors.pheFactor || 0;
          protein_Factor = item.factors.proteinFactor || 0;
          setPheFactor(phe_Factor);
          setProteinFactor(protein_Factor);
        }
        const _phe_factor = phe_Factor;
        const _protein_factor = protein_Factor;
        const _phe = item.phe;
        const _protein = item.protein;
        const _quantity = item.quantity;

        setFinalPhe(formatValue(_phe));
        setFinalProtein(formatValue(_protein));
        setFinalQuantity(formatValue(_quantity));
        setPheFactor(Number(_phe_factor));
        setProteinFactor(Number(_protein_factor));
        setGramWeight(Number(item.gram_weight));
        setSelectedUnit(item.unit);

        const portions_ = item.portions?.map((portion: any) => ({
          id: portion?.name,
          text: portion?.name,
          gramWeight: Number(portion?.gram_weight),
        }));
        setPortions(portions_ || []);
      }
    }
    return () => {
      mounted = false;
    };
  }, [JSON.stringify(item)]); //

  const renderInputField = () => {
    const inputProps = {
      placeholderTextColor: config.colors.gray,
      keyboardType: "numeric" as KeyboardTypeOptions,
      style: styles.pheTextInput,
      onFocus: handleFocus,
      onBlur: handleBlur,
    };

    const conversion = (field: string) => (text: string) => {
      let quan_ = finalQuantity;
      let prot_ = finalProtein;
      let phe_ = finalPhe;
      if (text.trim() !== "" && Number(text) > 0) {
        setQuantityError(null);
      } else {
        switch (field) {
          case "servings":
            setQuantityError(
              "Quantity is required and must be greater than 0."
            );
            break;
          case "protein":
            break;
          case "phe":
            break;
        }
      }

      if (text === "") {
        switch (field) {
          case "servings":
            quan_ = "";
            setFinalQuantity("");
            break;
          case "protein":
            prot_ = "";
            setFinalProtein("");
            break;
          case "phe":
            phe_ = "";
            setFinalPhe("");
            break;
        }
        return;
      }

      // Validate input
      if (!validateNonZeroNumericInput(text)) return;

      switch (field) {
        case "servings": {
          const quan_change = DietTrackerService.onQtyChange(
            proteinFactor,
            pheFactor,
            Number(text),
            gramWeight
          );
          setFinalPhe(quan_change.phe.toFixed(2));
          setFinalProtein(quan_change.protein.toFixed(2));
          setFinalQuantity(text);
          quan_ = text;
          prot_ = quan_change.protein.toFixed(2);
          phe_ = quan_change.phe.toFixed(2);
          break;
        }
        case "protein": {
          const protein_change = DietTrackerService.onProteinChange(
            proteinFactor,
            pheFactor,
            Number(text),
            gramWeight
          );
          setFinalPhe(protein_change.phe.toFixed(2));
          setFinalProtein(text);
          setFinalQuantity(protein_change.quantity.toFixed(2));
          quan_ = protein_change.quantity.toFixed(2);
          prot_ = text;
          phe_ = protein_change.phe.toFixed(2);
          break;
        }
        case "phe": {
          const phe_change = DietTrackerService.onPheChange(
            proteinFactor,
            pheFactor,
            Number(text),
            gramWeight
          );
          setFinalPhe(text);
          setFinalProtein(phe_change.protein.toFixed(2));
          setFinalQuantity(phe_change.quantity.toFixed(2));
          quan_ = phe_change.quantity.toFixed(2);
          prot_ = phe_change.protein.toFixed(2);
          phe_ = text;
          break;
        }
      }

      if (onUpdate) {
        onUpdate({
          ...(item || {}),
          quantity: quan_,
          unit: selectedUnit,
          protein: prot_,
          phe: phe_,
          gram_weight: gramWeight,
        });
      }
    };

    const handleUnitChange = (newUnit: Portion) => {
      // If quantity exists, recalculate based on new unit conversion
      setSelectedUnit(newUnit);
      const portion = portions.find((p) => p.text === newUnit);
      const __gramWeight = Number(portion?.gramWeight || 0);
      setGramWeight(__gramWeight);
      const unit_change = DietTrackerService.onQtyChange(
        proteinFactor,
        pheFactor,
        Number(finalQuantity || 0),
        __gramWeight
      );
      setFinalPhe(unit_change.phe.toFixed(2));
      setFinalProtein(unit_change.protein.toFixed(2));
      setFinalQuantity(unit_change.quantity.toFixed(2));
      if (onUpdate) {
        onUpdate({
          ...(item || {}),
          quantity: unit_change.quantity.toFixed(2),
          unit: newUnit,
          protein: unit_change.protein.toFixed(2),
          phe: unit_change.phe.toFixed(2),
          gram_weight: __gramWeight,
        });
      }
    };

    const inputDetails = {
      Quantity: {
        value: finalQuantity, // Get value from the item
        onChangeText: conversion("servings"),
        placeholder: "Enter quantity",
      },
      Protein: {
        value: finalProtein, // Get value from the item
        onChangeText: conversion("protein"),
        placeholder: "Enter protein amount",
        unit: "g",
      },
      Phe: {
        value: finalPhe,
        onChangeText: conversion("phe"),
        placeholder: "Enter phe amount",
        unit: "mg",
      },
    }[selectedTab];

    return (
      <View
        style={[
          styles.pheInputContent,
          isFocused && styles.pheTextInputFocused,
        ]}
      >
        <CustomTextInput
          {...inputDetails}
          {...inputProps}
          unit={inputDetails?.unit}
        />
        {selectedTab === "Quantity" && (
          <Dropdown
            isVisible={showDropdown}
            data={portions || []}
            selectedValue={selectedUnit}
            setSelectedValue={handleUnitChange}
            onToggle={() => setShowDropdown((prevState) => !prevState)}
            buttonStyle={styles.dropdownButton}
          />
        )}
      </View>
    );
  };

  function formatValue(value: number | string): string {
    if (typeof value === "number") {
      return value.toFixed(2);
    }
    return value;
  }

  return (
    <View style={styles.container}>
      <Typography.H2 style={styles.mealName}>
        {item.description || "Scanned Meal"}
      </Typography.H2>
      <TabSelector
        tabs={["Quantity", "Protein", "Phe"]}
        selectedTab={selectedTab}
        onTabPress={setSelectedTab}
        style={{ backgroundColor: colors.tabItemBg }}
      />
      {/* <RenderInputField /> */}
      {renderInputField()}
      <RNCheckbox.FreeFood
        value={!!item?.user_flags?.is_free}
        onSelect={(val) => {
          onUpdate?.({
            ...item,
            user_flags: { ...(item.user_flags || {}), is_free: val },
          });
        }}
      />

      {/* Error Message Below Input Field */}
      {quantityError ? (
        <Typography.B3 style={styles.errorText}>{quantityError}</Typography.B3>
      ) : null}
      <View style={styles.nutrientsContainer}>
        <View style={styles.nutrientBox}>
          <Typography.H1 style={styles.nutrientValue}>
            {item?.user_flags?.is_free ? freeFoodText : `${finalPhe || "0"} mg`}
          </Typography.H1>
          <Typography.B2 style={styles.nutrientLabel}>Phe</Typography.B2>
        </View>
        <View style={styles.nutrientBox}>
          <Typography.H1 style={styles.nutrientValue}>
            {item?.user_flags?.is_free
              ? freeFoodText
              : `${finalProtein || "0"} g`}
          </Typography.H1>
          <Typography.B2 style={styles.nutrientLabel}>Protein</Typography.B2>
        </View>
      </View>
      <Button.Outline
        onPress={() => {
          onRemove();
        }}
        style={styles.outlineButton}
      >
        <Typography.B1
          style={[Common.textBold, { color: colors.textPrimaryWhite }]}
        >
          Remove Food
        </Typography.B1>
      </Button.Outline>
    </View>
  );
};

export default AISuggestionsItem;
