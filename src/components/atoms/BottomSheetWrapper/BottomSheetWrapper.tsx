import React, { useCallback, useMemo, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Modal,
  Dimensions,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
  Pressable,
} from "react-native";
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import { BlurView } from "@react-native-community/blur";
import Typography from "@/components/atoms/Typography/Typography";
import bottomSheetStyles from "./BottomSheetWrapper.style";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import getBottomSheetStyles from "./BottomSheetWrapper.style";

const { height: screenHeight } = Dimensions.get("window");

export interface BottomSheetWrapperProps {
  isVisible: boolean;
  onClose: () => void;
  onBack?: () => void; // Optional callback for Back button
  snapPoints?: Array<string | number>;
  children: React.ReactNode;
  height?: number | string; // Custom height in pixels or percentage
  closeText?: string;
}

const BottomSheetWrapper: React.FC<BottomSheetWrapperProps> = ({
  isVisible,
  onClose,
  onBack,
  snapPoints,
  children,
  height,
  closeText = "Close",
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);

  const bottomSheetStyles: any = useDynamicStyles(getBottomSheetStyles)

  // Convert height to a numeric value if it's a percentage
  const calculatedHeight = useMemo(() => {
    if (typeof height === "string" && height.includes("%")) {
      return (parseFloat(height) / 100) * screenHeight;
    }
    return typeof height === "number" ? height : screenHeight * 0.9;
  }, [height]);

  // Default snap points based on custom height or percentage
  const defaultSnapPoints = useMemo(() => {
    return snapPoints || [calculatedHeight];
  }, [snapPoints, calculatedHeight]);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      bottomSheetRef.current?.snapToIndex(0);
    } else {
      bottomSheetRef.current?.close();
    }
  }, [isVisible]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
      }
    },
    [onClose]
  );

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        opacity={0.5}
        pressBehavior="close"
        disappearsOnIndex={-1}
        appearsOnIndex={0}
      >
        <Pressable style={StyleSheet.absoluteFill} onPress={Keyboard.dismiss}>
          <BlurView
            style={bottomSheetStyles.blurBackground}
            blurType="dark"
            blurAmount={10}
            reducedTransparencyFallbackColor="black"
          />
        </Pressable>
      </BottomSheetBackdrop>
    ),
    []
  );

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={StyleSheet.absoluteFill}>
          <BlurView
            style={bottomSheetStyles.blurBackground}
            blurType="dark"
            blurAmount={10}
            reducedTransparencyFallbackColor="black"
          />
          <BottomSheet
            ref={bottomSheetRef}
            snapPoints={defaultSnapPoints}
            enablePanDownToClose={false}
            android_keyboardInputMode="adjustResize"
            enableDynamicSizing
            keyboardBehavior="interactive"
            keyboardBlurBehavior="restore"
            backdropComponent={renderBackdrop}
            handleComponent={null}
            onChange={handleSheetChanges}
            backgroundStyle={{ backgroundColor: "transparent" }}
          >
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <BottomSheetView
                style={[
                  bottomSheetStyles.contentContainer,
                  { height: calculatedHeight }, // Ensure custom height is applied
                ]}
              >
                <View style={bottomSheetStyles.headerContainer}>
                  {onBack ? (
                    <TouchableOpacity
                      hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                      onPress={onBack}
                    >
                      <Typography.B1 style={bottomSheetStyles.backButtonText}>
                        Back
                      </Typography.B1>
                    </TouchableOpacity>
                  ) : (
                    <View />
                  )}
                  <TouchableOpacity
                    hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                    onPress={onClose}
                  >
                    <Typography.B1 style={bottomSheetStyles.closeButtonText}>
                      {closeText}
                    </Typography.B1>
                  </TouchableOpacity>
                </View>
                {children}
              </BottomSheetView>
            </TouchableWithoutFeedback>
          </BottomSheet>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default BottomSheetWrapper;
