import React, { useState, useEffect, useRef } from "react";
import { View, Modal, TouchableOpacity, Image, Alert, StyleSheet } from "react-native";
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
} from "react-native-vision-camera";
import { launchImageLibrary } from "react-native-image-picker";
import styles from "./CustomCamera.styles";
import Icons from "@/theme/assets/images/svgs/icons";
import Typography from "../Typography/Typography";
import { SafeScreen } from "@/components/template";
import { CameraTipsCarousel } from "../CameraTipsCarousel/CameraTipsCarousel";

interface CustomCameraProps {
  isVisible: boolean;
  onClose: () => void;
  onPhotoCaptured: (uri: string) => void;
}

const CustomCamera: React.FC<CustomCameraProps> = ({
  isVisible,
  onClose,
  onPhotoCaptured,
}) => {
  const [cameraPosition, setCameraPosition] = useState<"front" | "back">(
    "back"
  );
  const [flash, setFlash] = useState<"on" | "off">("off");
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);
  const device = useCameraDevice(cameraPosition);
  const { requestPermission } = useCameraPermission();
  const cameraRef = useRef<Camera>(null);

  // useEffect(() => {

  //   const requestPermissions = async () => {
  //     const isGranted = await requestPermission();
  //     if (!isGranted) {
  //       console.error("Camera permission not granted.");
  //       Alert.alert("Error", "Enable camera permission");
  //       onClose();
  //     }
  //   };
  //   requestPermissions();
  // }, []);

  const openMediaPicker = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: "photo",
        selectionLimit: 1,
      });

      if (result?.assets?.length) {
        const photoUri = result?.assets?.[0]?.uri || null;
        setSelectedMedia(photoUri);
        if (photoUri) {
          onPhotoCaptured(photoUri);
        }
        onClose();
      } else if (result.didCancel) {
        onClose();
      } else {
        onClose();
        Alert.alert("Error", "Failed to pick an image.");
      }
    } catch (error) {
      // console.error("Error opening media picker:", error);
      Alert.alert("Error", "Failed to open the media picker.");
    }
  };

  const handleCapture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePhoto();
        if (photo?.uri) {
          onPhotoCaptured(photo?.uri);
        } else if (photo.path) {
          onPhotoCaptured(photo.path);
        } else {
          console.error("Photo capture returned no URI or path.");
        }
        onClose();
      } catch (error) {
        console.error("Error capturing photo:", error);
      }
    }
  };

  if (!device) {
    return (
      <View style={styles.center} />
    );
  }

  return (
    <Modal visible={isVisible} animationType="slide" onRequestClose={onClose}>
      <SafeScreen>
        <View style={styles.container}>
          {/* Camera View */}
          <Camera
            ref={cameraRef}
            style={StyleSheet.absoluteFill}
            device={device}
            isActive={true}
            // photoQualityBalance="speed"
            photo={true}
            torch={flash === "on" ? "on" : "off"}
          />

          {/* Flash Button */}
          <TouchableOpacity
            style={styles.flashButton}
            onPress={() => setFlash(flash === "on" ? "off" : "on")}
          >
            <Icons.Flash />
          </TouchableOpacity>

          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Icons.Close width={40} height={40} hasNoCircle />
          </TouchableOpacity>

          <CameraTipsCarousel  style={styles.carousel} />
          {/* Bottom Controls */}
          <View style={styles.bottomControls}>
            {/* Media Picker */}
            <TouchableOpacity
              style={styles.mediaPicker}
              onPress={openMediaPicker}
            >
              {selectedMedia && (
                <Image
                  source={{ uri: selectedMedia }}
                  style={styles.mediaPreview}
                />
              )}
            </TouchableOpacity>

            {/* Shutter Button */}
            <TouchableOpacity
              style={styles.shutterButton}
              onPress={() => handleCapture()}
            >
              <View style={styles.shutterInner} />
            </TouchableOpacity>

            {/* Camera Toggle */}
            <TouchableOpacity
              style={styles.cameraToggle}
              onPress={() =>
                setCameraPosition(cameraPosition === "back" ? "front" : "back")
              }
            >
              <Icons.Switch width={45} height={45} />
            </TouchableOpacity>
          </View>
        </View>
      </SafeScreen>

    </Modal>
  );
};

export default CustomCamera;
