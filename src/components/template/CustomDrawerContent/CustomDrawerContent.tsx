import React, { useCallback, useEffect, useState } from "react";
import { View, TouchableOpacity, Linking, useColorScheme } from "react-native";
import {
  DrawerContentScrollView,
  DrawerItem,
  DrawerContentComponentProps,
} from "@react-navigation/drawer";
import { useAuth0 } from "react-native-auth0";

import { useTheme } from "@/theme";
import { useAppDispatch } from "@/store";
import Common from "@/theme/common.style";
import { logout } from "@/store/slices/authSlice";
import getCustomDrawerStyle from "./CustomDrawerContent.styles";
import { Typography } from "@/components/atoms";
import SafeScreen from "../SafeScreen/SafeScreen";
import Icons from "@/theme/assets/images/svgs/icons";
import Button from "@/components/atoms/Button/Button";
import { resetProfile } from "@/store/slices/userSlice";
import GenericModal from "@/components/molecules/GenericModal/GenericModal";
import { resetPheState } from "@/store/slices/pheAllowanceSlice";
import { resetDietState } from "@/store/slices/dietTrackerSlice";
import { useRemoteConfig } from "@/context/RemoteConfigContext";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import { setTutorial } from "@/store/slices/onboardingSlice";

interface CustomDrawerContentProps extends DrawerContentComponentProps {}

const CustomDrawerContent: React.FC<CustomDrawerContentProps> = (props) => {
  const dispatch = useAppDispatch();
  const [modalVisible, setModalVisible] = useState(false);
  const { clearCredentials } = useAuth0();
  const { gutters, colors, variant } = useTheme();
  const styles: any = useDynamicStyles(getCustomDrawerStyle);
  const { remoteConfig } = useRemoteConfig();
  const { setAnalyticsEvent } = useAnalytics();

  const VideoIcon = variant === "dark" ? Icons.VideoWhiteIcon : Icons.VideoIcon;

  const handleLogout = async () => {
    setModalVisible(false); // Close the modal after logout
    dispatch(resetProfile());
    dispatch(resetPheState());
    dispatch(resetDietState());

    await clearCredentials();

    dispatch(logout());
  };

  const toggleModal = useCallback(() => {
    setModalVisible(!modalVisible);
  }, [modalVisible]);

  return (
    <>
      <DrawerContentScrollView
        {...props}
        contentContainerStyle={styles.container}
      >
        <SafeScreen>
          <View style={styles.header}>
            <Typography.H2 style={Common.textBold}>Menu</Typography.H2>
            <TouchableOpacity onPress={() => props.navigation.goBack()}>
              <Icons.Close
                style={styles.profileIcon}
                color={colors.textPrimary}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.drawerItemContainer}>
            <View style={styles.drawerItemWrapper}>
              <DrawerItem
                style={gutters.paddingLeft_16}
                label={(props: { focused: boolean; color: string }) => (
                  <Typography.B1 style={styles.neg_margin_12}>
                    Patient Contacts
                  </Typography.B1>
                )}
                onPress={() =>
                  props.navigation.navigate("Home", {
                    screen: "PatientContactsStack",
                    params: {
                      screen: "PatientContactsScreen",
                    },
                  })
                }
                icon={() => (
                  <Icons.Contacts
                    width={24}
                    height={24}
                    color={colors.textPrimary}
                  />
                )}
              />
            </View>
            <View style={styles.drawerItemWrapper}>
              <DrawerItem
                style={gutters.paddingLeft_16}
                label={(props: { focused: boolean; color: string }) => (
                  <Typography.B1 style={styles.neg_margin_12}>
                    User Profile
                  </Typography.B1>
                )}
                onPress={() => props.navigation.navigate("UserProfile")}
                icon={() => (
                  <Icons.Profile
                    color={colors.textPrimary}
                    width={24}
                    height={24}
                  />
                )}
              />
            </View>

            <View style={styles.drawerItemWrapper}>
              <DrawerItem
                style={gutters.paddingLeft_16}
                label={() => (
                  <Typography.B1 style={styles.neg_margin_12}>
                    How to Videos
                  </Typography.B1>
                )}
                onPress={() => {
                  dispatch(setTutorial(true));
                  props.navigation.navigate("HowToVideos");
                }}
                icon={() => (
                  <VideoIcon
                    width={24}
                    height={24}
                    color={colors.textPrimary}
                  />
                )}
              />
            </View>
            <View style={styles.drawerItemWrapper}>
              <DrawerItem
                style={gutters.paddingLeft_16}
                label={() => (
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography.B1 style={styles.neg_margin_12}>
                      Get Support
                    </Typography.B1>
                    <Icons.ArrowLink width={12} height={12} />
                  </View>
                )}
                onPress={() => {
                  Linking.openURL(`mailto:${remoteConfig.supportEmail}`)
                    .then(() =>
                      setAnalyticsEvent(analyticsEventType.custom, {
                        event: "support_contact_email_tapped",
                        item_id: "support_contact_email_tapped",
                        action: "User initiated contact email	",
                      })
                    )
                    .catch((err) => console.error("Failed to open URL:", err));
                }}
                icon={() => <Icons.Support color={colors.textPrimary} />}
              />
            </View>
            <View style={styles.drawerItemWrapper}>
              <DrawerItem
                style={gutters.paddingLeft_16}
                label={() => (
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography.B1 style={styles.neg_margin_12}>
                      Privacy Policy
                    </Typography.B1>
                    <Icons.ArrowLink width={12} height={12} />
                  </View>
                )}
                onPress={() => Linking.openURL(remoteConfig.linkPrivacyPolicy)}
                icon={() => (
                  <Icons.CheckBoard
                    width={24}
                    height={24}
                    color={colors.textPrimary}
                  />
                )}
              />
            </View>
          </View>

          <Button.Main style={styles.logoutButton} onPress={toggleModal}>
            <Typography.B1 style={styles.logoutButtonText}>
              Logout
            </Typography.B1>
          </Button.Main>
        </SafeScreen>
      </DrawerContentScrollView>

      <GenericModal
        isVisible={modalVisible}
        onClose={toggleModal}
        onConfirm={handleLogout}
        headerText={"Confirm Logout"}
        bodyText={"Are you sure you want to log out?"}
        confirmText={"Yes"}
        closeText={"No"}
      />
    </>
  );
};

export default CustomDrawerContent;
