import { View } from "react-native";
import { useAuth0 } from "react-native-auth0";
import { useTranslation } from "react-i18next";
import React, { useCallback, useLayoutEffect, useState } from "react";

import { useAppDispatch } from "@/store";
import Common from "@/theme/common.style";
import { LOGIN_OPTIONS } from "@/constants/auth0";
import { SafeScreen } from "@/components/template";
import Icons from "@/theme/assets/images/svgs/icons";
import { Button, Typography } from "@/components/atoms";
import { login, setIdToken } from "@/store/slices/authSlice";
import { createUserThunk, setLoading } from "@/store/slices/userSlice";

import jwtDecode from "jwt-decode";
import getLoginStyle from "./LoginSceen.style";
import axiosInstance from "@/services/axiosInstance";
import Loading from "@/components/atoms/Loading/Loading";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";


interface DecodedToken {
  email?: string;
  name?: string;
  nickname?: string;
  sub?: string;
  phone?: string;
}

const LoginScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { authorize, user, getCredentials } = useAuth0();
  const { t } = useTranslation(["login"]);

  const styles: any = useDynamicStyles(getLoginStyle);
  const loginWithOption = useCallback(
    (connection: string) => async () => {
      try {
        setIsLoading(true)
        await authorize({
          connection,
          scope: 'openid profile email phone',
          additionalParameters: {
            prompt: 'login'
          }
        }, { ephemeralSession: false });
        const response = await getCredentials();

        if (response?.idToken) {
          axiosInstance.defaults.headers.common['Authorization'] = response?.idToken;

          const decodedToken = jwtDecode(response?.idToken) as DecodedToken;
          if (!decodedToken?.email && decodedToken?.sub?.split?.("|")?.[0]?.includes('facebook')) {
            decodedToken.email = `${decodedToken?.sub}@facebook.com`
          }

          const payloadForUser = {
            email: decodedToken?.email || '',
            name: decodedToken?.name || '',
            nickname: decodedToken?.nickname || '',
            userSubId: decodedToken?.sub || '',
            phoneNumber: decodedToken?.phone || ''
          }

          dispatch(createUserThunk(payloadForUser)).unwrap().then((res) => {
          })

          dispatch(setIdToken(response.idToken));
          dispatch(login(user));
        }
      } catch (e) {
      }
      finally {
        setIsLoading(false)
      }
    },
    []
  );

  useLayoutEffect(() => {
    dispatch(setLoading(false))
  }, [])

  return (
    <SafeScreen>
      <View style={styles.container}>
        <Icons.Logo style={styles.logo} fill={styles.logoFill.color} />
        <Typography.H0 style={styles.loginText}>Get Started</Typography.H0>

        <View style={styles.btnGroupView}>
          <Button.Auth onPress={loginWithOption(LOGIN_OPTIONS.GOOGLE)} disabled={isLoading}>
            <View style={Common.rowJustifyCenter}>
              <View style={styles.baseView}>
                <Icons.Google width={18} height={18} />
                <Typography.B1 style={styles.authText}>
                  {t("continueWith", { connection: "Google" })}
                </Typography.B1>
              </View>
            </View>
          </Button.Auth>

          <Button.Auth onPress={loginWithOption(LOGIN_OPTIONS.FACEBOOK)} disabled={isLoading}>
            <View style={Common.rowJustifyCenter}>
              <View style={styles.baseView}>
                <Icons.Facebook width={18} height={18} />
                <Typography.B1 style={styles.authText}>
                  {t("continueWith", { connection: "Facebook" })}
                </Typography.B1>
              </View>
            </View>
          </Button.Auth>

          <Button.Auth onPress={loginWithOption(LOGIN_OPTIONS.MICROSOFT)} disabled={isLoading}>
            <View style={Common.rowJustifyCenter}>
              <View style={styles.baseView}>
                <Icons.Microsoft width={18} height={18} />
                <Typography.B1 style={styles.authText}>
                  {t("continueWith", { connection: "Microsoft" })}
                </Typography.B1>
              </View>
            </View>
          </Button.Auth>

          <Button.Auth onPress={loginWithOption(LOGIN_OPTIONS.APPLE)} disabled={isLoading}>
            <View style={Common.rowJustifyCenter}>
              <View style={styles.baseView}>
                <Icons.Apple width={18} height={22.19} />
                <Typography.B1 style={styles.authText}>
                  {t("continueWith", { connection: "Apple" })}
                </Typography.B1>
              </View>
            </View>
          </Button.Auth>
        </View>
      </View>
      {!isLoading ? null : <Loading />}
    </SafeScreen>
  );
};

export default LoginScreen;
