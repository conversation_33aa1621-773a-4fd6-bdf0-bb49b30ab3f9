import React, { useState, useCallback, useEffect } from "react";
import { <PERSON><PERSON>, Linking, View } from "react-native";
import { useMMKV } from "react-native-mmkv";
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView } from "react-native-safe-area-context";

import { useTheme } from "@/theme";
import Common from "@/theme/common.style";
import { Variant } from "@/types/theme/config";
import { getOnboardingStyle } from "./Onboarding.style";
import { Button, Typography } from "@/components/atoms";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";
import SettingsOption from "@/components/molecules/SettingsOption/SettingsOption";
import SettingsToggle from "@/components/molecules/SettingsToggle/SettingsToggle";
import {
  disableDeviceOnHub,
  getFCMToken,
  registerDeviceOnHub,
  requestPermissionForNotification,
} from "@/hooks/useNotifee";
import { useSelector } from "react-redux";
import {
  getToggleNotify,
  setToggleLoader,
  setToggleNotify,
} from "@/store/slices/notificationSlice";
import { RootState, useAppDispatch } from "@/store";
import { setPrefrenceSelected } from "@/store/slices/onboardingSlice";

const UserPreferences = () => {
  const navigation = useNavigation();
  const [selectedMode, setSelectedMode] = useState("dark");
  const { toggleLoading, toggleNotify } = useSelector(
    (state: RootState) => state.notification
  );
  const lastAcknowledgedVersion = useSelector(
    (state: RootState) => state.settings.lastAcknowledgedVersion
  );
  const mmkv = useMMKV();
  const { changeTheme } = useTheme();
  const styles: any = useDynamicStyles(getOnboardingStyle);

  const dispatch = useAppDispatch();

  useEffect(() => {
    let mounted = true;
    if (mounted) {
      dispatch(getToggleNotify(getFCMToken() || ""));
    }

    return () => {
      mounted = false;
    };
  }, []);

  function handleSaveConfiguration() {
    mmkv.set("theme", selectedMode);
    dispatch(setPrefrenceSelected(true));
    (navigation.navigate as any)("DashboardGuide");
  }

  const handleUIModeChange = (mode: string) => {
    setSelectedMode(mode);
    changeTheme(mode as Variant);
  };

  const showNotificationAlert = () => {
    Alert.alert(
      "Enable Notifications",
      "Notifications are currently disabled. Would you like to enable them in the settings?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Go to Settings",
          onPress: () => {
            Linking.openSettings();
          },
        },
      ],
      { cancelable: false }
    );
  };

  const handlePushNotificationToggle = async (value: boolean) => {
    if (value) {
      const isGranted = await requestPermissionForNotification();
      if (isGranted == "granted") {
        try {
          await registerDeviceOnHub(value);
          dispatch(setToggleNotify(true));
        } catch (error) {
          dispatch(setToggleNotify(false));
          dispatch(setToggleLoader(false));
          console.error("Error registering device:", error);
        }
      } else {
        showNotificationAlert();
      }
    } else disableDeviceOnHub(value);
  };

  const getSeletedStyle = useCallback(
    (mode: string, currentMode: string, style: string) => {
      if (mode === currentMode) {
        return styles[style];
      }
      return {};
    },
    [styles]
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.userConfigContainer}>
        <Typography.H3 style={Common.textBold}>Your Preferences</Typography.H3>
        <View style={styles.settingsContainer}>
          <SettingsOption title="UI Mode" disabled />

          <View style={styles.accordionContainer}>
            <Button.Outline
              onPress={() => handleUIModeChange("dark")}
              style={[
                styles.modeButton,
                getSeletedStyle("dark", selectedMode, "selectedButton"),
              ]}
            >
              <Typography.B2
                style={[
                  styles.buttonOutlineText,
                  getSeletedStyle("dark", selectedMode, "selectedButtonText"),
                ]}
              >
                Dark
              </Typography.B2>
            </Button.Outline>
            <Button.Outline
              onPress={() => handleUIModeChange("light")}
              style={[
                styles.modeButton,
                getSeletedStyle("light", selectedMode, "selectedButton"),
              ]}
            >
              <Typography.B2
                style={[
                  styles.buttonOutlineText,
                  getSeletedStyle("light", selectedMode, "selectedButtonText"),
                ]}
              >
                Light
              </Typography.B2>
            </Button.Outline>
            <Button.Outline
              onPress={() => handleUIModeChange("default")}
              style={[
                styles.modeButton,
                getSeletedStyle("default", selectedMode, "selectedButton"),
              ]}
            >
              <Typography.B2
                style={[
                  styles.buttonOutlineText,
                  getSeletedStyle(
                    "default",
                    selectedMode,
                    "selectedButtonText"
                  ),
                ]}
              >
                System
              </Typography.B2>
            </Button.Outline>
          </View>
          <SettingsToggle
            title="Push Notifications"
            isToggleOn={toggleNotify || false}
            onToggle={handlePushNotificationToggle}
            disabled={toggleLoading}
          />
        </View>
      </View>
      <Button.Yellow
        style={styles.btnContainer}
        onPress={handleSaveConfiguration}
        activeOpacity={0.85}
      >
        <Typography.B2 style={styles.btnText}>Save</Typography.B2>
      </Button.Yellow>
    </SafeAreaView>
  );
};

export default UserPreferences;
