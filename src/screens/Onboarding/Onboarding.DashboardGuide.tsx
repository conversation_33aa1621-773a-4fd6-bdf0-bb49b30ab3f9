import React, { useRef, useState, useCallback } from "react";
import { Pressable, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { getOnboardingStyle } from "./Onboarding.style";
import { Button, Typography } from "@/components/atoms";
import {
  useNavigation,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import Video, { VideoRef } from "react-native-video";
import Icons from "@/theme/assets/images/svgs/icons";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  completeOnboarding,
  selectOnboarding,
} from "@/store/slices/onboardingSlice";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

const DashboardGuide = () => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  const { isTutorial } = useAppSelector(selectOnboarding);

  const videoRef = useRef<VideoRef>(null);
  const [isPaused, setPaused] = useState<boolean>(true);
  const { setAnalyticsEvent } = useAnalytics();
  const styles: any = useDynamicStyles(getOnboardingStyle);

  const background = require("@/theme/assets/videos/Dashboard.mp4");
  const posterImage = require("@/theme/assets/images/onboading/Dashboard.png");

  function handleNextPress() {
    navigation.navigate("MedicationGuide" as never);
  }

  const dispatch = useAppDispatch();
  function handleSkipAll() {
    dispatch(completeOnboarding());

    if (isTutorial) {
      navigation.navigate("Home" as never);
      return;
    }

    setAnalyticsEvent(analyticsEventType.custom, {
      event: "onboarded",
      item_id: "onboarded",
      action: "User completes onboarding",
    });
  }

  async function handleVideoPlayer() {
    if (isPaused) {
      videoRef?.current?.resume();
      setPaused(false);
    } else {
      videoRef?.current?.pause();
      setPaused(true);
    }
  }

  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus
      return () => {
        // When screen goes out of focus
        if (videoRef?.current) {
          videoRef.current.pause();
          setPaused(true);
        }
      };
    }, [])
  );

  return (
    <SafeAreaView>
      <View style={styles.flex_center}>
        <View style={styles.contentContainer}>
          <View style={styles.videoContainer}>
            <Pressable onPress={handleVideoPlayer}>
              <Video
                resizeMode="contain"
                source={background}
                poster={posterImage}
                // posterResizeMode is deprecated, using resizeMode instead
                paused={isPaused || !isFocused}
                muted={false}
                onLoad={() => videoRef?.current?.pause()}
                ref={videoRef}
                style={styles.backgroundVideo}
              />
              {isPaused && (
                <View
                  style={{
                    position: "absolute",
                    alignSelf: "center",
                    top: "45%",
                  }}
                >
                  <Icons.PlayIcon />
                </View>
              )}
            </Pressable>
          </View>

          <View style={styles.guidesContainer}>
            <View style={styles.titleContainer}>
              <Typography.H2>A Guide to</Typography.H2>
              <Typography.H1 style={styles.appTitle}>Dashboard</Typography.H1>

              <View style={styles.textContainerMini}>
                <Typography.B2>
                  Keep your medication and formula routine on track with helpful
                  reminders and a clear, easy-to-use dashboard.
                </Typography.B2>
              </View>

              <Button.Yellow
                style={styles.btnContainer}
                onPress={handleNextPress}
                activeOpacity={0.85}
              >
                <Typography.B2 style={styles.btnText}>Next</Typography.B2>
              </Button.Yellow>

              <Pressable style={styles.skipAll} onPress={handleSkipAll}>
                <Typography.B2 style={styles.primaryText}>
                  Skip All
                </Typography.B2>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default DashboardGuide;
