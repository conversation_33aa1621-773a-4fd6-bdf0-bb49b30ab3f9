import React, { useRef, useState, useCallback } from "react";
import { Pressable, View } from "react-native";
import Video, { VideoRef } from "react-native-video";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  useFocusEffect,
  useIsFocused,
  useNavigation,
} from "@react-navigation/native";
import { getOnboardingStyle } from "./Onboarding.style";
import { Button, Typography } from "@/components/atoms";
import { useAppDispatch, useAppSelector } from "@/store";
import Icons from "@/theme/assets/images/svgs/icons";
import {
  completeOnboarding,
  selectOnboarding,
} from "@/store/slices/onboardingSlice";
import { analyticsEventType, useAnalytics } from "@/hooks/useAnalytics";
import { useDynamicStyles } from "@/theme/hooks/useDynamicStyles";

const LogFoodGuide = () => {
  const isFocused = useIsFocused();
  const videoRef = useRef<VideoRef>(null);
  const navigation = useNavigation();
  const [isPaused, setPaused] = useState<boolean>(true);
  const { setAnalyticsEvent } = useAnalytics();
  const styles: any = useDynamicStyles(getOnboardingStyle);
  const { isTutorial } = useAppSelector(selectOnboarding);

  const background = require("@/theme/assets/videos/Labs.mp4");
  const posterImage = require("@/theme/assets/images/onboading/Labs.png");

  const dispatch = useAppDispatch();
  function handleComplete() {
    dispatch(completeOnboarding());

    if (isTutorial) {
      navigation.navigate("Home" as never);
      return;
    }
    setAnalyticsEvent(analyticsEventType.custom, {
      event: "onboarded",
      item_id: "onboarded",
      action: "User completes onboarding",
    });
  }

  async function handleVideoPlayer() {
    if (isPaused) {
      videoRef?.current?.resume();
      setPaused(false);
    } else {
      videoRef?.current?.pause();
      setPaused(true);
    }
  }

  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus
      return () => {
        // When screen goes out of focus
        if (videoRef?.current) {
          videoRef.current.pause();
          setPaused(true);
        }
      };
    }, [])
  );

  return (
    <SafeAreaView>
      <View style={styles.flex_center}>
        <View style={styles.contentContainer}>
          <View style={styles.videoContainer}>
            <Pressable onPress={handleVideoPlayer}>
              <Video
                resizeMode="cover"
                source={background}
                poster={posterImage}
                paused={isPaused || !isFocused}
                muted={false}
                onLoad={() => videoRef?.current?.pause()}
                ref={videoRef}
                style={styles.backgroundVideo}
              />
              {isPaused && (
                <View
                  style={{
                    position: "absolute",
                    alignSelf: "center",
                    top: "45%",
                  }}
                >
                  <Icons.PlayIcon />
                </View>
              )}
            </Pressable>
          </View>
          <View style={styles.guidesContainer}>
            <View style={styles.titleContainer}>
              <Typography.H2>A Guide to</Typography.H2>
              <Typography.H1 style={styles.appTitle}>Labs</Typography.H1>

              <View style={styles.textContainerMini}>
                <Typography.B2>
                  Track and review your Phe levels over time.
                </Typography.B2>
              </View>

              <Button.Yellow
                style={styles.btnContainer}
                onPress={handleComplete}
                activeOpacity={0.85}
              >
                <Typography.B2 style={styles.btnText}>Next</Typography.B2>
              </Button.Yellow>

              <Pressable style={styles.skipAll} onPress={handleComplete}>
                <Typography.B2 style={styles.primaryText}>
                  Skip All
                </Typography.B2>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default LogFoodGuide;
